import yaml
import os
from pydantic import BaseModel, Field, ValidationError
from typing import List, Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class SmartAPIConfig(BaseModel):
    api_key: str = Field(..., env="SMARTAPI_API_KEY")
    client_code: str = Field(..., env="SMARTAPI_USERNAME")
    password: str = Field(..., env="SMARTAPI_PASSWORD")
    totp_secret: Optional[str] = Field(None, env="SMARTAPI_TOTP_TOKEN")

class ExecutionConfig(BaseModel):
    algorithms: List[str] = Field(['market', 'limit', 'twap', 'vwap', 'implementation_shortfall'], alias='execution_algorithms')
    max_slippage: float = Field(0.02, env="MAX_SLIPPAGE")
    order_timeout: int = Field(300, env="ORDER_TIMEOUT")
    retry_attempts: int = Field(3, env="RETRY_ATTEMPTS")
    retry_backoff_factor: float = Field(2.0, env="RETRY_BACKOFF_FACTOR")
    lot_sizes: Dict[str, int] = Field({}, description="Dynamic lot sizes for different underlyings, e.g., {'BANKNIFTY': 25}")

class OptionsExecutionAgentConfig(BaseModel):
    smartapi: SmartAPIConfig
    execution: ExecutionConfig

    @classmethod
    def load_config(cls, config_path: str = "config/options_execution_config.yaml"):
        """
        Loads configuration from a YAML file and overrides with environment variables.
        """
        config_data: Dict[str, Any] = {}
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config_data = yaml.safe_load(f)
            logger.info(f"⚙️ Configuration loaded from {config_path}")
        else:
            logger.warning(f"⚠️ Configuration file not found at {config_path}. Relying solely on environment variables.")

        # Flatten config_data for Pydantic's env_prefix to work, or manually apply env vars
        # Pydantic's Field(env=...) handles direct env var overrides if names match.
        # For nested structures, we might need to manually merge.

        # Manual merge for nested structures if environment variables are not directly mapped
        # Example: SMARTAPI_API_KEY will override smartapi.api_key
        # For execution.lot_sizes, environment variables like BANKNIFTY_LOT_SIZE could be handled
        # by the calling code or a custom root validator if the structure is complex.

        # For simplicity, Pydantic's env var handling (via Field(env=...)) is used.
        # Any environment variable matching the `env` parameter in Field will override.
        # For `lot_sizes`, we'll explicitly check for env vars like BANKNIFTY_LOT_SIZE.

        # Initialize with loaded YAML data
        try:
            config = cls(**config_data)
        except ValidationError as e:
            logger.error(f"❌ Configuration validation error from YAML: {e}")
            raise

        # Apply environment variable overrides for lot_sizes
        for key, value in os.environ.items():
            if key.endswith('_LOT_SIZE') and key.isupper():
                underlying = key.replace('_LOT_SIZE', '')
                try:
                    config.execution.lot_sizes[underlying] = int(value)
                    logger.info(f"⚙️ Overriding {underlying}_LOT_SIZE with environment variable: {value}")
                except ValueError:
                    logger.warning(f"⚠️ Invalid integer value for environment variable {key}: {value}")
        
        return config
