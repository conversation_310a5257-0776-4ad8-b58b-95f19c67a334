import logging
import polars as pl
from pathlib import Path
from typing import Dict, Any, Optional, List
import asyncio
import json

logger = logging.getLogger(__name__)

class InstrumentManager:
    """
    Manages the mapping of trading symbols to SmartAPI instrument tokens.
    Supports loading instruments from a file and potentially fetching them via API.
    """
    def __init__(self, instrument_data_path: str = "config/options_symbols.json"):
        self.instrument_data_path = Path(instrument_data_path)
        self._instrument_map: Dict[str, Dict[str, Any]] = {} # Maps trading_symbol -> instrument_details
        self._token_map: Dict[str, Dict[str, Any]] = {} # Maps token -> instrument_details
        self._is_initialized = False

        logger.info("📚 InstrumentManager initialized.")

    async def initialize(self, smartapi_client=None) -> bool:
        """
        Initializes the InstrumentManager by loading instrument data.
        If a smartapi_client is provided, it can be used to fetch instruments if the file is not found.
        """
        if self._is_initialized:
            logger.info("InstrumentManager already initialized.")
            return True

        try:
            if self.instrument_data_path.exists():
                await self._load_instruments_from_file()
            elif smartapi_client:
                logger.warning(f"Instrument data file not found at {self.instrument_data_path}. Attempting to fetch from SmartAPI.")
                await self._fetch_and_save_instruments(smartapi_client)
            else:
                logger.error("No instrument data file found and no SmartAPI client provided to fetch instruments.")
                return False
            
            self._is_initialized = True
            logger.info("✅ InstrumentManager initialized successfully.")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize InstrumentManager: {e}")
            return False

    async def _load_instruments_from_file(self):
        """Loads instrument data from a JSON file."""
        try:
            with open(self.instrument_data_path, 'r') as f:
                instruments_list = json.load(f)
            
            for instrument in instruments_list:
                trading_symbol = instrument.get('tradingsymbol')
                token = instrument.get('token')
                if trading_symbol and token:
                    self._instrument_map[trading_symbol] = instrument
                    self._token_map[token] = instrument
            logger.info(f"Loaded {len(self._instrument_map)} instruments from {self.instrument_data_path}.")
        except Exception as e:
            logger.error(f"❌ Error loading instruments from file {self.instrument_data_path}: {e}")
            raise

    async def _fetch_and_save_instruments(self, smartapi_client):
        """
        Fetches all NFO instruments from SmartAPI and saves them to a file.
        This is a placeholder and might require specific SmartAPI endpoints
        or a more complex fetching logic.
        """
        logger.warning("Fetching instruments from SmartAPI is a placeholder. SmartAPI client might not have a direct 'get all instruments' endpoint.")
        # In a real scenario, you might need to:
        # 1. Use a specific SmartAPI endpoint to download master contracts.
        # 2. Parse a CSV file provided by the broker.
        # 3. Use a dedicated 'search' API if available to find instruments.

        # For demonstration, we'll simulate fetching or assume a method exists.
        # Example: smartapi_client.get_all_instruments('NFO')
        # If such an API doesn't exist, this method would need to be adapted
        # to a manual download and parsing process.
        
        # Placeholder for actual SmartAPI call
        # For now, we'll create a dummy file if it doesn't exist
        if not self.instrument_data_path.exists():
            dummy_instruments = [
                {"instrumenttype": "OPTIDX", "symbol": "BANKNIFTY", "name": "BANKNIFTY", "expiry": "25JUL2025", "strike": 47800, "optiontype": "CE", "tradingsymbol": "BANKNIFTY25JUL47800CE", "token": "35000"},
                {"instrumenttype": "OPTIDX", "symbol": "BANKNIFTY", "name": "BANKNIFTY", "expiry": "25JUL2025", "strike": 47800, "optiontype": "PE", "tradingsymbol": "BANKNIFTY25JUL47800PE", "token": "35001"},
                {"instrumenttype": "OPTIDX", "symbol": "NIFTY", "name": "NIFTY", "expiry": "25JUL2025", "strike": 22000, "optiontype": "CE", "tradingsymbol": "NIFTY25JUL22000CE", "token": "35002"},
            ]
            self.instrument_data_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.instrument_data_path, 'w') as f:
                json.dump(dummy_instruments, f, indent=4)
            logger.info(f"Created dummy instrument data file at {self.instrument_data_path}.")
            await self._load_instruments_from_file() # Load the newly created file
        else:
            logger.info("Instrument data file already exists, not fetching dummy data.")


    def get_token_by_symbol(self, trading_symbol: str) -> Optional[str]:
        """
        Retrieves the instrument token for a given trading symbol.
        """
        instrument = self._instrument_map.get(trading_symbol)
        if instrument:
            return instrument.get('token')
        logger.warning(f"Token not found for trading symbol: {trading_symbol}")
        return None

    def get_symbol_by_token(self, token: str) -> Optional[str]:
        """
        Retrieves the trading symbol for a given instrument token.
        """
        instrument = self._token_map.get(token)
        if instrument:
            return instrument.get('tradingsymbol')
        logger.warning(f"Trading symbol not found for token: {token}")
        return None

    def get_instrument_details(self, trading_symbol: str) -> Optional[Dict[str, Any]]:
        """
        Retrieves full instrument details for a given trading symbol.
        """
        return self._instrument_map.get(trading_symbol)

    def get_all_instruments(self) -> List[Dict[str, Any]]:
        """Returns a list of all loaded instrument details."""
        return list(self._instrument_map.values())

    def resolve_option_symbol_to_token(self, underlying: str, strike_price: int, option_type: str, expiry: str) -> Optional[str]:
        """
        Resolves the option symbol and then its token for SmartAPI.
        Example: BANKNIFTY, 47800, CE, 2025-07-25 -> BANKNIFTY25JUL47800CE -> token
        This method relies on the loaded instrument map.
        """
        try:
            # Format expiry date: YYYY-MM-DD to DDMMMYY (e.g., 2025-07-25 -> 25JUL25)
            expiry_dt = datetime.strptime(expiry, "%Y-%m-%d")
            # Angel One uses a specific format for month (e.g., JUL for July)
            # For year, it's usually last two digits.
            # This might need adjustment based on actual SmartAPI symbol conventions.
            formatted_expiry = expiry_dt.strftime("%d%b%y").upper()
            
            # Construct the trading symbol
            # Example: BANKNIFTY25JUL47800CE
            trading_symbol = f"{underlying}{formatted_expiry}{strike_price}{option_type}"
            logger.debug(f"🔍 Constructed trading symbol: {trading_symbol}")
            
            token = self.get_token_by_symbol(trading_symbol)
            if token:
                logger.info(f"🔍 Resolved option symbol {trading_symbol} to token: {token}")
                return token
            else:
                logger.warning(f"⚠️ Could not find token for resolved symbol: {trading_symbol}")
                return None
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to resolve option symbol to token for {underlying} {strike_price} {option_type} {expiry}: {e}")
            return None
