import logging
from enum import Enum, auto
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class OrderStatus(Enum):
    """
    Defines the possible states for an order in its lifecycle.
    """
    PENDING = auto()        # Order created, but not yet sent to broker
    QUEUED = auto()         # Order sent to broker, awaiting confirmation
    OPEN = auto()           # Order is active in the market, partially or fully unfilled
    PARTIALLY_FILLED = auto() # Order has been partially filled
    COMPLETE = auto()       # Order fully filled
    CANCELLED = auto()      # Order cancelled by user or system
    REJECTED = auto()       # Order rejected by broker
    FAILED_FALLBACK = auto() # Order failed to modify/retry after timeout
    EXITED_SL = auto()      # Position exited due to Stop Loss hit
    EXITED_TP = auto()      # Position exited due to Target Profit hit
    EXITED_EOD = auto()     # Position exited due to End-of-Day square-off
    UNKNOWN_EXTERNAL_STATUS = auto() # Status unknown, possibly completed/cancelled externally

class OrderStateManager:
    """
    Manages the state transitions and data for individual orders.
    Provides methods for updating order status and associated details.
    """
    def __init__(self):
        self._orders: Dict[str, Dict[str, Any]] = {} # Stores order_id -> order_data

    def add_order(self, order_id: str, initial_data: Dict[str, Any]):
        """
        Adds a new order to the state manager with an initial status.
        """
        if order_id in self._orders:
            logger.warning(f"Order {order_id} already exists. Overwriting with new data.")
        
        # Ensure initial status is set correctly
        initial_data['status'] = OrderStatus.PENDING.name # Default to PENDING before sending to broker
        initial_data['entry_time'] = datetime.now().isoformat()
        initial_data['last_update_time'] = datetime.now().isoformat()
        self._orders[order_id] = initial_data
        logger.info(f"Order {order_id} added with initial status: {initial_data['status']}")

    def update_order_status(self, order_id: str, new_status: OrderStatus, details: Optional[Dict[str, Any]] = None) -> bool:
        """
        Updates the status of an existing order.
        """
        if order_id not in self._orders:
            logger.warning(f"Order {order_id} not found. Cannot update status.")
            return False
        
        current_order = self._orders[order_id]
        old_status = current_order['status']

        if old_status == new_status.name:
            logger.debug(f"Order {order_id} status is already {new_status.name}. No change.")
            return True

        # Perform state transition checks if necessary (e.g., cannot go from COMPLETE to QUEUED)
        # For now, we allow direct transitions, but this can be enhanced.
        
        current_order['status'] = new_status.name
        current_order['last_update_time'] = datetime.now().isoformat()
        if details:
            current_order.update(details) # Update other details like filled_qty, avg_price
        
        logger.info(f"Order {order_id} status changed: {old_status} -> {new_status.name}")
        return True

    def get_order(self, order_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieves the current state of an order.
        """
        return self._orders.get(order_id)

    def get_all_orders(self) -> Dict[str, Dict[str, Any]]:
        """
        Returns a copy of all managed orders.
        """
        return self._orders.copy()

    def remove_order(self, order_id: str) -> bool:
        """
        Removes an order from the state manager.
        Typically used after an order is fully completed, cancelled, or rejected and no longer needs active monitoring.
        """
        if order_id in self._orders:
            del self._orders[order_id]
            logger.info(f"Order {order_id} removed from state manager.")
            return True
        logger.warning(f"Order {order_id} not found. Cannot remove.")
        return False

    def update_order_details(self, order_id: str, details: Dict[str, Any]) -> bool:
        """
        Updates specific details of an existing order without changing its status.
        """
        if order_id not in self._orders:
            logger.warning(f"Order {order_id} not found. Cannot update details.")
            return False
        
        self._orders[order_id].update(details)
        self._orders[order_id]['last_update_time'] = datetime.now().isoformat()
        logger.debug(f"Order {order_id} details updated.")
        return True

    def set_orders(self, orders: Dict[str, Dict[str, Any]]):
        """
        Sets the entire dictionary of orders. Useful for loading from persistence.
        Ensures that status values are converted to string names if they are Enum members.
        """
        self._orders = {}
        for order_id, order_data in orders.items():
            if 'status' in order_data and isinstance(order_data['status'], OrderStatus):
                order_data['status'] = order_data['status'].name
            self._orders[order_id] = order_data
        logger.info(f"OrderStateManager loaded with {len(self._orders)} orders.")
