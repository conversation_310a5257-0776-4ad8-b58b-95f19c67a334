import asyncio
import logging
import os
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

# Lazy import SmartConnect to improve startup time
# from SmartApi import SmartConnect

logger = logging.getLogger(__name__)

class SmartAPIConnectionError(Exception):
    """Custom exception for SmartAPI connection issues."""
    pass

class SmartAPISessionError(Exception):
    """Custom exception for SmartAPI session generation/refresh failures."""
    pass

class TokenManager:
    """
    Manages SmartAPI token generation, refreshing, and session lifecycle.
    Implements a robust token refresh mechanism.
    """
    def __init__(self, api_key: str, client_code: str, password: str, totp_secret: Optional[str] = None):
        self.api_key = api_key
        self.client_code = client_code
        self.password = password
        self.totp_secret = totp_secret
        
        self._smartapi_client = None # Will be initialized lazily
        self._access_token: Optional[str] = None
        self._refresh_token: Optional[str] = None
        self._jwt_token_expiry: Optional[datetime] = None
        self._refresh_task: Optional[asyncio.Task] = None
        self._is_refresh_loop_running = False

        logger.info("🔑 TokenManager initialized.")

    async def _get_smartapi_client(self):
        """Lazily initializes and returns the SmartConnect client."""
        if self._smartapi_client is None:
            try:
                from SmartApi import SmartConnect
                self._smartapi_client = SmartConnect(api_key=self.api_key)
                logger.info("SmartConnect client initialized lazily.")
            except ImportError:
                logger.error("SmartApi library not found. Please install it.")
                raise
            except Exception as e:
                logger.error(f"Failed to initialize SmartConnect client: {e}")
                raise SmartAPIConnectionError(f"Failed to initialize SmartConnect client: {e}")
        return self._smartapi_client

    async def generate_session(self) -> bool:
        """
        Generates a new SmartAPI session and stores tokens.
        Returns True on success, False otherwise.
        """
        try:
            client = await self._get_smartapi_client()
            logger.info(f"Attempting to generate SmartAPI session for client code: {self.client_code}")
            data = client.generateSession(
                self.client_code,
                self.password,
                self.totp_secret
            )
            
            if data and data.get('status'):
                self._access_token = data['data']['jwtToken']
                self._refresh_token = data['data']['refreshToken']
                # SmartAPI JWT tokens typically expire in 24 hours.
                # We'll set refresh interval to be well before that, e.g., 23 hours.
                self._jwt_token_expiry = datetime.now() + timedelta(hours=23) 
                
                client.setAccessToken(self._access_token)
                client.setRefreshToken(self._refresh_token)
                logger.info("🔑 SmartAPI session generated successfully.")
                return True
            else:
                message = data.get('message', 'Unknown error')
                logger.error(f"SmartAPI session generation failed: {message}")
                raise SmartAPISessionError(f"SmartAPI session generation failed: {message}")
        except SmartAPIConnectionError:
            raise # Re-raise if client initialization failed
        except Exception as e:
            logger.error(f"Exception during SmartAPI session generation: {e}")
            raise SmartAPISessionError(f"Exception during SmartAPI session generation: {e}")

    async def refresh_access_token(self) -> bool:
        """
        Refreshes the SmartAPI access token using the refresh token.
        Returns True on success, False otherwise.
        """
        if not self._refresh_token:
            logger.warning("No refresh token available. Cannot refresh session.")
            return False

        try:
            client = await self._get_smartapi_client()
            logger.info("Attempting to refresh SmartAPI access token.")
            data = client.generateToken(self._refresh_token)
            
            if data and data.get('status'):
                self._access_token = data['data']['jwtToken']
                # Refresh token might also be updated, or remain the same.
                # Assuming it might be updated, we'll store it.
                self._refresh_token = data['data'].get('refreshToken', self._refresh_token)
                self._jwt_token_expiry = datetime.now() + timedelta(hours=23) # Reset expiry
                
                client.setAccessToken(self._access_token)
                client.setRefreshToken(self._refresh_token)
                logger.info("🔑 SmartAPI access token refreshed successfully.")
                return True
            else:
                message = data.get('message', 'Unknown error')
                logger.error(f"SmartAPI token refresh failed: {message}. Attempting full session regeneration.")
                # If refresh fails, try to generate a new session
                return await self.generate_session()
        except SmartAPIConnectionError:
            raise # Re-raise if client initialization failed
        except Exception as e:
            logger.error(f"Exception during SmartAPI token refresh: {e}. Attempting full session regeneration.")
            # If any exception during refresh, try to generate a new session
            return await self.generate_session()

    def get_access_token(self) -> Optional[str]:
        """Returns the current access token."""
        return self._access_token

    def get_smartapi_client(self):
        """Returns the initialized SmartConnect client."""
        if self._smartapi_client is None:
            logger.warning("SmartAPI client not yet initialized. Call generate_session first.")
        return self._smartapi_client

    async def _token_refresh_loop(self):
        """Background task to periodically refresh the access token."""
        self._is_refresh_loop_running = True
        while self._is_refresh_loop_running:
            try:
                if self._jwt_token_expiry and datetime.now() >= self._jwt_token_expiry:
                    logger.info("JWT token nearing expiry, attempting refresh.")
                    success = await self.refresh_access_token()
                    if not success:
                        logger.error("Failed to refresh token in background loop. Attempting to regenerate session.")
                        await self.generate_session() # Try full regeneration
                
                # Check every 30 minutes, or more frequently if expiry is closer
                await asyncio.sleep(1800) # 30 minutes
            except asyncio.CancelledError:
                logger.info("Token refresh loop cancelled.")
                break
            except Exception as e:
                logger.error(f"Error in token refresh loop: {e}")
                await asyncio.sleep(60) # Wait a bit longer on error

    def start_token_refresh_loop(self):
        """Starts the background token refresh task."""
        if not self._is_refresh_loop_running:
            logger.info("Starting SmartAPI token refresh loop.")
            self._refresh_task = asyncio.create_task(self._token_refresh_loop())
        else:
            logger.info("Token refresh loop is already running.")

    async def stop_token_refresh_loop(self):
        """Stops the background token refresh task."""
        if self._refresh_task:
            logger.info("Stopping SmartAPI token refresh loop.")
            self._is_refresh_loop_running = False
            self._refresh_task.cancel()
            try:
                await self._refresh_task
            except asyncio.CancelledError:
                logger.info("Token refresh task successfully cancelled.")
            self._refresh_task = None
