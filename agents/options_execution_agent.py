#!/usr/bin/env python3
"""
Options Execution Agent - Smart Options Order Execution

Features:
🚀 1. Smart Order Execution
- Multi-leg strategy execution
- Optimal order timing
- Slippage minimization
- Market impact reduction

📊 2. Order Management
- Order lifecycle management
- Partial fill handling
- Order modification and cancellation
- Execution quality monitoring

⚡ 3. SmartAPI Integration
- NFO segment order placement
- Real-time order status
- Margin calculation
- Position management

🎯 4. Execution Algorithms
- TWAP (Time Weighted Average Price)
- VWAP (Volume Weighted Average Price)
- Implementation Shortfall
- Market-on-Close execution
"""

import asyncio
import logging
from utils.heartbeat import create_heartbeat
import polars as pl
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
import os
from dotenv import load_dotenv

# Lazy import SmartAPI components to improve startup time
from SmartApi import SmartConnect, SmartWebSocket

# Import new modules
from agents.execution.token_manager import TokenManager, SmartAPISessionError, SmartAPIConnectionError
from agents.execution.instrument_manager import InstrumentManager
from agents.execution.order_state_manager import OrderState<PERSON>anager, OrderStatus
from agents.execution.twap_execution import TWAPExecution
from agents.execution.vwap_execution import VWAPExecution
from agents.execution.implementation_shortfall_execution import ImplementationShortfallExecution
from agents.execution.config_manager import OptionsExecutionAgentConfig # Import the new config manager

# Import other agents
# These will be lazy-loaded where possible, but their classes need to be defined for type hinting
# For now, keep them here. Lazy loading will be applied to their instantiation.
from agents.options_risk_management_agent import OptionsRiskManagementAgent
from agents.options_market_monitoring_agent import OptionsMarketMonitoringAgent
from agents.alerting_notifications_system import AlertingNotificationsSystem
from agents.options_performance_analysis_agent import OptionsPerformanceAnalysisAgent
from agents.options_ai_training_agent import OptionsAITrainingAgent

# Load environment variables from .env file
load_dotenv()

logger = logging.getLogger(__name__)

class OptionsExecutionAgent:
    """Options Execution Agent for smart order execution"""
    
    def __init__(self, config_path: str = "config/options_execution_config.yaml", dry_run: bool = True):
        self.config_path = config_path
        self.config: Optional[OptionsExecutionAgentConfig] = None # Type hint for structured config
        self.is_running = False
        
        self.heartbeat = create_heartbeat('execution')
        self.dry_run = dry_run

        # New: Token, Instrument, and Order State Managers
        self.token_manager: Optional[TokenManager] = None
        self.instrument_manager: Optional[InstrumentManager] = None
        self.order_state_manager: OrderStateManager = OrderStateManager()
        
        # SmartAPI client will be managed by TokenManager
        self.smartapi_client: Optional[SmartConnect] = None # Type hint as SmartConnect

        # Other agents - will be initialized lazily
        self.risk_agent: Optional[OptionsRiskManagementAgent] = None
        self.market_data_agent: Optional[OptionsMarketMonitoringAgent] = None
        self.alerting_agent: Optional[AlertingNotificationsSystem] = None
        self.performance_agent: Optional[OptionsPerformanceAnalysisAgent] = None
        self.ai_training_agent: Optional[OptionsAITrainingAgent] = None

        # Execution Algorithms
        self.twap_executor: Optional[TWAPExecution] = None
        self.vwap_executor: Optional[VWAPExecution] = None
        self.is_executor: Optional[ImplementationShortfallExecution] = None

        self.eod_square_off_time = datetime.now().replace(hour=15, minute=20, second=0, microsecond=0)
        self.persistence_file = Path("data/execution/active_orders.parquet")
        
        logger.info("🚀 [INIT] Options Execution Agent initialized")
    
    async def initialize(self, **kwargs):
        """Initialize the agent"""
        try:
            self.config = OptionsExecutionAgentConfig.load_config(self.config_path)
            
            # Initialize TokenManager and SmartAPI connection
            if not self.dry_run:
                self.token_manager = TokenManager(
                    api_key=self.config.smartapi.api_key,
                    client_code=self.config.smartapi.client_code,
                    password=self.config.smartapi.password,
                    totp_secret=self.config.smartapi.totp_secret
                )
                await self.token_manager.generate_session()
                self.smartapi_client = self.token_manager.get_smartapi_client()
                self.token_manager.start_token_refresh_loop() # Start background token refresh

            # Initialize InstrumentManager
            self.instrument_manager = InstrumentManager()
            await self.instrument_manager.initialize(smartapi_client=self.smartapi_client)

            # Load previously active orders and reconstruct state
            await self._load_active_orders()
            if not self.dry_run:
                await self._reconstruct_active_orders_from_api() # Complement persistence with live API data

            # Initialize other agents (lazy loading for these)
            # These imports are moved to the point of instantiation to reduce initial startup time
            from agents.options_risk_management_agent import OptionsRiskManagementAgent
            self.risk_agent = OptionsRiskManagementAgent()
            await self.risk_agent.initialize(**kwargs)

            from agents.options_market_monitoring_agent import OptionsMarketMonitoringAgent
            self.market_data_agent = OptionsMarketMonitoringAgent()
            await self.market_data_agent.initialize(**kwargs)

            from agents.alerting_notifications_system import AlertingNotificationsSystem
            self.alerting_agent = AlertingNotificationsSystem()
            await self.alerting_agent.initialize(**kwargs)

            from agents.options_performance_analysis_agent import OptionsPerformanceAnalysisAgent
            self.performance_agent = OptionsPerformanceAnalysisAgent()
            await self.performance_agent.initialize(**kwargs)

            from agents.options_ai_training_agent import OptionsAITrainingAgent
            self.ai_training_agent = OptionsAITrainingAgent()
            await self.ai_training_agent.initialize(**kwargs)

            # Initialize execution algorithms
            self.twap_executor = TWAPExecution(self)
            self.vwap_executor = VWAPExecution(self, self.market_data_agent)
            await self.vwap_executor.initialize() # Initialize VWAP to load profiles if any
            self.is_executor = ImplementationShortfallExecution(self, self.market_data_agent)

            logger.info("✅ [SUCCESS] Options Execution Agent initialized successfully")
            self.heartbeat.start_heartbeat()
            
            return True
        except (SmartAPIConnectionError, SmartAPISessionError, ValueError) as e:
            logger.error(f"❌ [ERROR] Critical SmartAPI initialization failed: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to initialize agent: {e}")
            return False
    
    # Removed _load_config as it's replaced by OptionsExecutionAgentConfig.load_config
    # The config is now accessed via self.config.smartapi.api_key etc.
    
    async def _init_smartapi(self):
        """
        This method is now largely handled by TokenManager.
        It remains as a placeholder for any direct SmartAPI client setup not covered by TokenManager.
        """
        if self.token_manager and self.token_manager.get_smartapi_client():
            self.smartapi_client = self.token_manager.get_smartapi_client()
            logger.info("SmartAPI client obtained from TokenManager.")
        else:
            logger.error("TokenManager not initialized or failed to provide SmartAPI client.")
            raise SmartAPIConnectionError("SmartAPI client not available.")

    def _resolve_option_symbol(self, underlying: str, strike_price: int, option_type: str, expiry: str) -> str:
        """
        Resolves the option symbol for SmartAPI.
        Now uses InstrumentManager to get the token.
        """
        if not self.instrument_manager:
            logger.error("InstrumentManager not initialized. Cannot resolve option symbol.")
            raise RuntimeError("InstrumentManager not initialized.")
        
        # The InstrumentManager's method already handles symbol construction and token lookup
        token = self.instrument_manager.resolve_option_symbol_to_token(
            underlying=underlying,
            strike_price=strike_price,
            option_type=option_type,
            expiry=expiry
        )
        if token:
            # We return the trading symbol here, as place_order expects it.
            # The token will be used internally by place_order if needed.
            # For now, we'll reconstruct the symbol for consistency with existing calls.
            expiry_dt = datetime.strptime(expiry, "%Y-%m-%d")
            formatted_expiry = expiry_dt.strftime("%d%b%y").upper()
            trading_symbol = f"{underlying}{formatted_expiry}{strike_price}{option_type}"
            logger.info(f"🔍 Resolved option symbol: {trading_symbol} (Token: {token})")
            return trading_symbol
        else:
            raise ValueError(f"Could not resolve option symbol for {underlying} {strike_price} {option_type} {expiry}")

    async def place_order(self, order_params: Dict[str, Any]) -> Optional[str]:
        """
        Places an order via Angel One SmartAPI.
        Automatically selects Exchange (NFO), Product type (MIS, NRML), Order type (LIMIT, MARKET).
        """
        # Prepare common order data for both dry run and live trading
        common_order_data = {
            "filled_qty": 0,
            "avg_price": 0,
            "signal_data": order_params, # Store original signal for tracking
            "transaction_type": order_params.get('transaction_type'),
            "tradingsymbol": order_params.get('tradingsymbol'),
            "quantity": order_params.get('quantity'),
            "price": order_params.get('price'),
            "order_type": order_params.get('order_type'),
            "product": order_params.get('product'),
            "exchange": order_params.get('exchange'),
            "variety": order_params.get('variety'),
            "retries": 0 # Initialize retries
        }

        if self.dry_run:
            order_id = f"DRYRUN_{datetime.now().timestamp()}_{order_params.get('tradingsymbol', 'UNKNOWN')}"
            logger.info(f"📝 [DRY RUN] Placing order: {order_params} -> Order ID: {order_id}")
            
            self.order_state_manager.add_order(order_id, {
                "order_id": order_id,
                "status": OrderStatus.QUEUED,
                **common_order_data
            })
            await self._save_active_orders()
            return order_id

        if not self.smartapi_client:
            logger.error("❌ SmartAPI client not initialized. Cannot place order.")
            return None

        try:
            # Default values for Angel One SmartAPI
            api_order_data = {
                "exchange": "NFO",
                "variety": order_params.get('variety', 'NORMAL'),
                "tradingsymbol": order_params['tradingsymbol'],
                "transaction_type": order_params['transaction_type'],
                "quantity": order_params['quantity'],
                "product": order_params.get('product', 'MIS'),
                "order_type": order_params.get('order_type', 'LIMIT'),
                "price": order_params.get('price', 0),
                "triggerprice": order_params.get('triggerprice', 0),
                "squareoff": order_params.get('squareoff', 0),
                "stoploss": order_params.get('stoploss', 0),
                "trailingStoploss": order_params.get('trailingStoploss', 0),
                "disclosedquantity": order_params.get('disclosedquantity', 0),
                "ordertag": order_params.get('ordertag', ''),
                "duration": order_params.get('duration', 'DAY')
            }
            loop = asyncio.get_running_loop()
            response = await loop.run_in_executor(None, self.smartapi_client.placeOrder, api_order_data)
            if response and response.get('status'):
                order_id = response['data']['orderid']
                logger.info(f"✅ Order placed successfully! Order ID: {order_id}, Details: {api_order_data}")
                
                self.order_state_manager.add_order(order_id, {
                    "order_id": order_id,
                    "status": OrderStatus.QUEUED,
                    **common_order_data,
                    **api_order_data # Store all API order data as well
                })
                self._log_trade_event(order_id, OrderStatus.QUEUED.name, api_order_data)
                await self._save_active_orders()
                return order_id
            else:
                message = response.get('message', 'Unknown error')
                logger.error(f"❌ Failed to place order: {message}, Details: {api_order_data}")
                self._log_trade_event("N/A", OrderStatus.REJECTED.name, {"reason": message, "order_params": api_order_data})
                return None
        except Exception as e:
            logger.error(f"❌ [ERROR] Exception while placing order: {e}, Order Params: {order_params}")
            self._log_trade_event("N/A", OrderStatus.REJECTED.name, {"reason": str(e), "order_params": order_params})
            return None

    async def modify_order(self, order_id: str, new_price: Optional[float] = None, new_quantity: Optional[int] = None) -> bool:
        """Modifies an existing order."""
        current_order = self.order_state_manager.get_order(order_id)
        if not current_order:
            logger.warning(f"⚠️ Order {order_id} not found in state manager. Cannot modify.")
            return False

        if self.dry_run:
            logger.info(f"📝 [DRY RUN] Modifying order {order_id}: new_price={new_price}, new_quantity={new_quantity}")
            update_details = {}
            if new_price is not None:
                update_details['price'] = new_price
            if new_quantity is not None:
                update_details['quantity'] = new_quantity
            
            if update_details:
                self.order_state_manager.update_order_details(order_id, update_details)
                logger.info(f"📝 [DRY RUN] Order {order_id} modified.")
                await self._save_active_orders()
                return True
            return False

        if not self.smartapi_client:
            logger.error("❌ SmartAPI client not initialized. Cannot modify order.")
            return False

        modify_data = {
            "variety": current_order['variety'],
            "orderid": order_id,
            "exchange": current_order['exchange'],
            "tradingsymbol": current_order['tradingsymbol'],
            "symboltoken": current_order.get('symboltoken', ''),
            "ordertype": current_order['order_type'],
            "producttype": current_order['product'],
            "duration": "DAY",
            "price": new_price if new_price is not None else current_order['price'],
            "quantity": new_quantity if new_quantity is not None else current_order['quantity'],
            "triggerprice": current_order.get('triggerprice', 0)
        }

        try:
            loop = asyncio.get_running_loop()
            response = await loop.run_in_executor(None, self.smartapi_client.modifyOrder, modify_data)
            if response and response.get('status'):
                logger.info(f"✅ Order {order_id} modified successfully.")
                self.order_state_manager.update_order_details(order_id, {
                    'price': modify_data['price'],
                    'quantity': modify_data['quantity']
                })
                await self._save_active_orders()
                return True
            else:
                message = response.get('message', 'Unknown error')
                logger.error(f"❌ Failed to modify order {order_id}: {message}")
                self._log_trade_event(order_id, "MODIFICATION_FAILED", {"reason": message, "modify_data": modify_data})
                return False
        except Exception as e:
            logger.error(f"❌ [ERROR] Exception while modifying order {order_id}: {e}")
            self._log_trade_event(order_id, "MODIFICATION_EXCEPTION", {"reason": str(e), "modify_data": modify_data})
            return False

    async def cancel_order(self, order_id: str) -> bool:
        """Cancels an existing order."""
        current_order = self.order_state_manager.get_order(order_id)
        if not current_order:
            logger.warning(f"⚠️ Order {order_id} not found in state manager. Cannot cancel.")
            return False

        if self.dry_run:
            logger.info(f"📝 [DRY RUN] Cancelling order {order_id}")
            self.order_state_manager.update_order_status(order_id, OrderStatus.CANCELLED)
            logger.info(f"📝 [DRY RUN] Order {order_id} cancelled.")
            await self._save_active_orders()
            return True

        if not self.smartapi_client:
            logger.error("❌ SmartAPI client not initialized. Cannot cancel order.")
            return False

        cancel_data = {
            "variety": current_order['variety'],
            "orderid": order_id,
            "exchange": current_order['exchange'],
            "tradingsymbol": current_order['tradingsymbol']
        }

        try:
            loop = asyncio.get_running_loop()
            response = await loop.run_in_executor(None, self.smartapi_client.cancelOrder, cancel_data)
            if response and response.get('status'):
                logger.info(f"✅ Order {order_id} cancelled successfully.")
                self.order_state_manager.update_order_status(order_id, OrderStatus.CANCELLED)
                await self._save_active_orders()
                return True
            else:
                message = response.get('message', 'Unknown error')
                logger.error(f"❌ Failed to cancel order {order_id}: {message}")
                self._log_trade_event(order_id, "CANCELLATION_FAILED", {"reason": message, "cancel_data": cancel_data})
                return False
        except Exception as e:
            logger.error(f"❌ [ERROR] Exception while cancelling order {order_id}: {e}")
            self._log_trade_event(order_id, "CANCELLATION_EXCEPTION", {"reason": str(e), "cancel_data": cancel_data})
            return False

    async def get_order_book(self) -> List[Dict[str, Any]]:
        """Retrieves the current order book from SmartAPI."""
        if self.dry_run:
            logger.info("📝 [DRY RUN] Getting order book.")
            # Simulate order book for dry run
            return list(self.order_state_manager.get_all_orders().values()) # Use order_state_manager

        if not self.smartapi_client:
            logger.error("❌ SmartAPI client not initialized. Cannot get order book.")
            return []

        try:
            loop = asyncio.get_running_loop()
            response = await loop.run_in_executor(None, self.smartapi_client.getOrderBook)
            if response and response.get('status'):
                logger.info("📚 Successfully retrieved order book.")
                return response['data']
            else:
                logger.error(f"❌ Failed to get order book: {response.get('message', 'Unknown error')}")
                return []
        except Exception as e:
            logger.error(f"❌ [ERROR] Exception while getting order book: {e}")
            return []

    async def _process_signal(self, signal: Dict[str, Any]) -> Optional[str]:
        """
        Receives structured trade signal from Signal Generation Agent and converts it
        into a SmartAPI-ready order object, then places the order.
        """
        logger.info(f"⚡ Processing signal: {signal}")
        try:
            # 1. Execution Risk Checks
            if self.risk_agent:
                # Check capital at risk, daily limits, duplicate signals, over-positioning
                # This is a simplified call; actual risk agent would have more detailed checks
                risk_check_passed, reason = await self.risk_agent.check_trade_risk(signal)
                if not risk_check_passed:
                    logger.warning(f"⚠️ Risk check failed for signal {signal.get('strategy_id', 'N/A')}: {reason}. Aborting order placement.")
                    await self._send_notification(f"🚫 Order Aborted: Risk check failed for {signal.get('underlying')}. Reason: {reason}")
                    return None

            # 2. Resolve option symbol
            trading_symbol = self._resolve_option_symbol(
                underlying=signal['underlying'],
                strike_price=signal['strike_price'],
                option_type=signal['option_type'],
                expiry=signal['expiry']
            )

            # 3. Determine transaction type
            transaction_type = "BUY" if signal['action'].upper() == "BUY" else "SELL"

            # 4. Determine product type (MIS for intraday, NRML for positional)
            product_type = signal.get('product_type', 'MIS') # Allow signal to specify product type

            # 5. Determine order type (LIMIT or MARKET)
            order_type = signal.get('order_type', 'LIMIT') # Allow signal to specify order type
            price = signal.get('entry_price', 0)

            # 6. Calculate quantity based on lot_size and underlying's lot size
            underlying_lot_size = self.config.execution.lot_sizes.get(signal['underlying'].upper(), 1) # Default to 1 if not found
            quantity = signal['lot_size'] * underlying_lot_size

            # 7. Slippage check refinement for MARKET orders
            if order_type == "MARKET":
                if self.market_data_agent:
                    # Fetch real-time bid/ask spread
                    quote = await self.market_data_agent.get_quote(trading_symbol)
                    if quote and quote.get('bid') and quote.get('ask'):
                        bid = quote['bid']
                        ask = quote['ask']
                        realtime_ltp = quote.get('ltp', (bid + ask) / 2) # Fallback to mid-price if LTP not directly available

                        # Compare against signal's entry_price if available, otherwise use mid-price as reference
                        reference_price = signal.get('entry_price', realtime_ltp)
                        
                        # For a BUY market order, we'll execute at 'ask'. For SELL, at 'bid'.
                        execution_price_estimate = ask if transaction_type == "BUY" else bid

                        slippage_abs = abs(execution_price_estimate - reference_price)
                        slippage_pct = (slippage_abs / reference_price) if reference_price else 0

                        if slippage_pct > self.config.execution.max_slippage:
                            logger.warning(f"⚠️ Potential slippage {slippage_pct:.2%} exceeds max allowed {self.config.execution.max_slippage:.2%}. Aborting market order for {trading_symbol}.")
                            await self._send_notification(f"🚫 Order Aborted: High slippage for {trading_symbol}. Signal Price: {signal.get('entry_price', 'N/A')}, Est. Exec Price: {execution_price_estimate:.2f}, Bid: {bid:.2f}, Ask: {ask:.2f}.")
                            return None
                        else:
                            logger.info(f"✅ Slippage check passed for {trading_symbol}. Est. Exec Price: {execution_price_estimate:.2f}, Slippage: {slippage_pct:.2%}.")
                    else:
                        logger.warning(f"⚠️ Could not fetch real-time bid/ask spread for slippage check for {trading_symbol}. Aborting market order.")
                        await self._send_notification(f"🚫 Order Aborted: Real-time bid/ask unavailable for {trading_symbol}.")
                        return None
                else:
                    logger.warning(f"⚠️ Market data agent not available for slippage check for {trading_symbol}. Aborting market order.")
                    await self._send_notification(f"🚫 Order Aborted: Market data agent unavailable for {trading_symbol}.")
                    return None

            order_params_for_placement = {
                "tradingsymbol": trading_symbol,
                "transaction_type": transaction_type,
                "exchange": "NFO",
                "order_type": order_type,
                "quantity": quantity,
                "price": price,
                "product": product_type,
                "variety": "NORMAL" # Default variety
            }
            
            # 8. Select and execute algorithm
            execution_algorithm = signal.get('execution_algorithm', 'market').lower()
            order_id = None

            if execution_algorithm == 'twap' and self.twap_executor:
                duration = signal.get('twap_duration_seconds', 300) # Default 5 minutes
                order_id = await self.twap_executor.execute_order(order_params_for_placement, quantity, duration)
            elif execution_algorithm == 'vwap' and self.vwap_executor:
                duration = signal.get('vwap_duration_seconds', 300) # Default 5 minutes
                order_id = await self.vwap_executor.execute_order(order_params_for_placement, quantity, duration)
            elif execution_algorithm == 'implementation_shortfall' and self.is_executor:
                order_id = await self.is_executor.execute_order(order_params_for_placement, quantity, signal_price)
            elif execution_algorithm in self.config.execution.algorithms: # Use configured algorithms
                order_id = await self.place_order(order_params_for_placement)
            else:
                logger.error(f"❌ Unknown or uninitialized execution algorithm: {execution_algorithm}. Falling back to direct placement.")
                order_id = await self.place_order(order_params_for_placement)

            if order_id:
                logger.info(f"✅ Signal translated and order placed. Order ID: {order_id}")
                # Store the original signal data with the order for later tracking
                # self.order_state_manager.get_order(order_id)['original_signal'] = signal # This needs to be handled by add_order
                # The signal data is already stored in common_order_data when add_order is called.
                await self._save_active_orders() # Save after updating active_orders
                await self._send_notification(f"✅ Order Placed: {transaction_type} {quantity} of {trading_symbol} @ {price} (Strategy: {signal.get('strategy_id')}, Algo: {execution_algorithm})")
                return order_id
            else:
                logger.error(f"❌ Failed to place order for signal: {signal}")
                await self._send_notification(f"❌ Order Failed: Could not place order for {trading_symbol} (Strategy: {signal.get('strategy_id')}, Algo: {execution_algorithm})")
                return None

        except Exception as e:
            logger.error(f"❌ [ERROR] Error processing signal {signal}: {e}")
            await self._send_notification(f"❌ Order Error: Exception processing signal for {signal.get('underlying')}: {e}")
            return None

    async def _monitor_signals(self):
        """Monitor trading signals for execution from signal generation agent"""
        while self.is_running:
            try:
                # Read signals from signal generation agent's output
                signals = await self._read_signals_from_file()

                if signals:
                    logger.info(f"📡 [SIGNALS] Received {len(signals)} signals for processing")

                    for signal in signals:
                        try:
                            # Validate signal before processing
                            if self._validate_signal(signal):
                                await self._process_signal(signal)
                            else:
                                logger.warning(f"⚠️ [SIGNAL] Invalid signal received: {signal}")
                        except Exception as e:
                            logger.error(f"❌ [ERROR] Error processing individual signal: {e}")
                else:
                    logger.debug("👀 [SIGNALS] No new signals available")

                await asyncio.sleep(10)  # Check every 10 seconds for new signals

            except Exception as e:
                logger.error(f"❌ [ERROR] Signal monitoring failed: {e}")
                await asyncio.sleep(30)  # Wait longer on error

    async def _read_signals_from_file(self) -> List[Dict[str, Any]]:
        """Read trading signals from signal generation agent's output file"""
        try:
            signals_file = Path("data/signals/latest_signals.json")
            processed_signals_file = Path("data/execution/processed_signals.json")

            if not signals_file.exists():
                return []

            # Load existing processed signals to avoid duplicates
            processed_signal_ids = set()
            if processed_signals_file.exists():
                try:
                    # Ensure file operations are asynchronous
                    loop = asyncio.get_running_loop()
                    with await loop.run_in_executor(None, open, processed_signals_file, 'r') as f:
                        processed_data = await loop.run_in_executor(None, json.load, f)
                        processed_signal_ids = set(processed_data.get('processed_ids', []))
                except json.JSONDecodeError:
                    logger.warning(f"⚠️ Could not decode processed_signals.json. Starting with empty processed_signal_ids.")
                    processed_signal_ids = set()
                except Exception as e:
                    logger.error(f"❌ Error loading processed signals: {e}")
                    processed_signal_ids = set()


            # Read new signals
            loop = asyncio.get_running_loop()
            with await loop.run_in_executor(None, open, signals_file, 'r') as f:
                signals_data = await loop.run_in_executor(None, json.load, f)

            # Filter out already processed signals
            new_signals = []
            for signal in signals_data.get('signals', []):
                signal_id = signal.get('signal_id') or f"{signal.get('underlying')}_{signal.get('timestamp')}"
                if signal_id not in processed_signal_ids:
                    new_signals.append(signal)
                    processed_signal_ids.add(signal_id)

            # Update processed signals file
            if new_signals:
                processed_signals_file.parent.mkdir(parents=True, exist_ok=True)
                loop = asyncio.get_running_loop()
                with await loop.run_in_executor(None, open, processed_signals_file, 'w') as f:
                    await loop.run_in_executor(None, json.dump, {'processed_ids': list(processed_signal_ids)}, f)

            return new_signals

        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to read signals from file: {e}")
            return []

    def _validate_signal(self, signal: Dict[str, Any]) -> bool:
        """Validate trading signal structure and content"""
        try:
            required_fields = ['underlying', 'strike_price', 'option_type', 'expiry', 'action', 'confidence', 'lot_size']

            # Check required fields
            for field in required_fields:
                if field not in signal:
                    logger.error(f"❌ [VALIDATION] Missing required field: {field} in signal: {signal}")
                    return False

            # Validate field values
            if signal['underlying'] not in self.config.execution.lot_sizes.keys(): # Validate against configured underlyings
                logger.error(f"❌ [VALIDATION] Invalid underlying: {signal['underlying']}")
                return False

            if signal['option_type'] not in ['CE', 'PE']:
                logger.error(f"❌ [VALIDATION] Invalid option type: {signal['option_type']}")
                return False

            if signal['action'] not in ['BUY', 'SELL']:
                logger.error(f"❌ [VALIDATION] Invalid action: {signal['action']}")
                return False

            if not (0.0 <= signal['confidence'] <= 1.0):
                logger.error(f"❌ [VALIDATION] Invalid confidence: {signal['confidence']}")
                return False
            
            if not isinstance(signal['lot_size'], int) or signal['lot_size'] <= 0:
                logger.error(f"❌ [VALIDATION] Invalid lot_size: {signal['lot_size']}")
                return False

            # Validate optional fields if present
            if 'entry_price' in signal and not isinstance(signal['entry_price'], (int, float)):
                logger.error(f"❌ [VALIDATION] Invalid entry_price: {signal['entry_price']}")
                return False
            
            if 'stoploss' in signal and not isinstance(signal['stoploss'], (int, float)):
                logger.error(f"❌ [VALIDATION] Invalid stoploss: {signal['stoploss']}")
                return False

            if 'target' in signal and not isinstance(signal['target'], (int, float)):
                logger.error(f"❌ [VALIDATION] Invalid target: {signal['target']}")
                return False

            return True

        except Exception as e:
            logger.error(f"❌ [ERROR] Signal validation failed: {e}")
            return False

    async def _manage_orders(self):
        """Manage active orders: update status, handle partial fills, slippage fallback, and intelligent retries."""
        while self.is_running:
            try:
                logger.info("🔄 Managing active orders...")
                all_active_orders = self.order_state_manager.get_all_orders()
                if not all_active_orders:
                    logger.info("No active orders to manage.")
                    await asyncio.sleep(10)
                    continue

                order_book = await self.get_order_book() # This is an API call, assumed async
                order_book_map = {order['orderid']: order for order in order_book}

                for order_id, local_order in list(all_active_orders.items()):
                    if local_order['status'] in [OrderStatus.COMPLETE, OrderStatus.CANCELLED, OrderStatus.REJECTED, OrderStatus.EXITED_SL, OrderStatus.EXITED_TP, OrderStatus.FAILED_MAX_RETRIES]:
                        continue # Already finalized

                    smartapi_order = order_book_map.get(order_id)
                    if smartapi_order:
                        new_status_str = smartapi_order.get('status', local_order['status'].name).upper()
                        new_status = getattr(OrderStatus, new_status_str, OrderStatus.UNKNOWN_EXTERNAL_STATUS)
                        filled_qty = smartapi_order.get('filledquantity', 0)
                        avg_price = smartapi_order.get('averageprice', 0)

                        # Update order details and log changes
                        update_needed = False
                        if new_status != local_order['status']:
                            logger.info(f"📊 Order {order_id} status changed: {local_order['status'].name} -> {new_status.name}")
                            self.order_state_manager.update_order_status(order_id, new_status)
                            self.order_state_manager.update_order_details(order_id, {'last_update_time': datetime.now().isoformat()})
                            await self._log_trade_event(order_id, new_status.name, smartapi_order)
                            update_needed = True

                        if filled_qty > local_order['filled_qty']:
                            logger.info(f"📈 Order {order_id} partial fill: {filled_qty} / {local_order['quantity']} @ {avg_price}")
                            self.order_state_manager.update_order_details(order_id, {
                                'filled_qty': filled_qty,
                                'avg_price': avg_price,
                                'last_update_time': datetime.now().isoformat()
                            })
                            await self._log_trade_event(order_id, "PARTIAL_FILL", smartapi_order)
                            update_needed = True
                        
                        if update_needed:
                            await self._save_active_orders()

                        # Handle slippage fallback (e.g., switch from limit to market after X seconds)
                        if local_order['order_type'] == 'LIMIT' and local_order['status'] == OrderStatus.OPEN:
                            entry_time = datetime.fromisoformat(local_order['entry_time'])
                            time_in_force = (datetime.now() - entry_time).total_seconds()
                            if time_in_force > self.config.execution.order_timeout:
                                logger.warning(f"⏰ Order {order_id} timed out. Attempting to modify to MARKET order.")
                                success = await self.modify_order(order_id, new_price=0, new_quantity=local_order['quantity'])
                                if success:
                                    self.order_state_manager.update_order_details(order_id, {'order_type': 'MARKET'})
                                    logger.info(f"✅ Order {order_id} modified to MARKET order.")
                                    await self._log_trade_event(order_id, "MODIFIED_TO_MARKET", {"reason": "timeout"})
                                    await self._save_active_orders()
                                else:
                                    logger.error(f"❌ Failed to modify order {order_id} to MARKET. Cancelling.")
                                    await self.cancel_order(order_id)
                                    self.order_state_manager.update_order_status(order_id, OrderStatus.REJECTED)
                                    await self._log_trade_event(order_id, "MODIFICATION_FAILED_CANCELED", {"reason": "failed to modify to market"})
                                    await self._save_active_orders()

                        # Intelligent Retry Mechanism
                        if new_status in [OrderStatus.REJECTED, OrderStatus.CANCELLED]:
                            retries = local_order.get('retries', 0)
                            if retries < self.config.execution.retry_attempts:
                                retry_delay = self.config.execution.retry_backoff_factor ** retries
                                logger.warning(f"⚠️ Order {order_id} {new_status.name}. Retrying in {retry_delay:.1f}s ({retries+1}/{self.config.execution.retry_attempts}). Reason: {smartapi_order.get('text', 'N/A')}")
                                
                                await asyncio.sleep(retry_delay)
                                
                                # Analyze rejection reason and adjust parameters (simplified)
                                original_signal_data = local_order['signal_data']
                                adjusted_order_params = original_signal_data.copy()
                                rejection_reason = smartapi_order.get('text', '').lower()

                                if "price out of range" in rejection_reason:
                                    # If price is out of range, try a market order or adjust price aggressively
                                    adjusted_order_params['order_type'] = 'MARKET'
                                    adjusted_order_params['price'] = 0
                                    logger.info(f"🔄 Adjusting order {order_id} to MARKET due to price out of range.")
                                elif "insufficient funds" in rejection_reason:
                                    # If insufficient funds, log and do not retry automatically
                                    logger.error(f"❌ Order {order_id} rejected due to insufficient funds. Not retrying.")
                                    self.order_state_manager.update_order_status(order_id, OrderStatus.REJECTED)
                                    await self._log_trade_event(order_id, "FAILED_INSUFFICIENT_FUNDS", smartapi_order)
                                    await self._save_active_orders()
                                    continue # Skip retry for this order

                                # Increment retry count and re-place
                                self.order_state_manager.update_order_details(order_id, {'retries': retries + 1})
                                await self.place_order(adjusted_order_params)
                                await self._send_notification(f"🔄 Order Retry: {order_id} {new_status.name}. Retrying...")
                                await self._log_trade_event(order_id, "RETRYING", {"attempt": retries + 1, "reason": smartapi_order.get('text', 'N/A'), "adjusted_params": adjusted_order_params})
                                await self._save_active_orders()
                            else:
                                logger.error(f"❌ Order {order_id} {new_status.name} after {retries} retries. Final status.")
                                await self._send_notification(f"❌ Order Failed: {order_id} {new_status.name} after max retries.")
                                await self._log_trade_event(order_id, "FAILED_MAX_RETRIES", smartapi_order)
                                self.order_state_manager.update_order_status(order_id, OrderStatus.FAILED_MAX_RETRIES)
                                await self._save_active_orders()

                    else:
                        # Order not found in SmartAPI order book.
                        if local_order['status'] not in [OrderStatus.COMPLETE, OrderStatus.CANCELLED, OrderStatus.REJECTED, OrderStatus.UNKNOWN_EXTERNAL_STATUS, OrderStatus.EXITED_SL, OrderStatus.EXITED_TP, OrderStatus.FAILED_MAX_RETRIES]:
                            logger.warning(f"⚠️ Order {order_id} not found in SmartAPI order book. Attempting individual status check.")
                            if (datetime.now() - datetime.fromisoformat(local_order['last_update_time'])).total_seconds() > 60:
                                logger.warning(f"⚠️ Order {order_id} still not found after 60s. Marking as UNKNOWN_EXTERNAL_STATUS.")
                                self.order_state_manager.update_order_status(order_id, OrderStatus.UNKNOWN_EXTERNAL_STATUS)
                                await self._log_trade_event(order_id, "UNKNOWN_STATUS", {"message": "Order not found in order book after timeout."})
                                await self._save_active_orders()

                await asyncio.sleep(10)
            except Exception as e:
                logger.error(f"❌ [ERROR] Order management failed: {e}")
                await asyncio.sleep(30) # Wait longer on error

    async def _log_trade_event(self, order_id: str, event_type: str, details: Dict[str, Any]):
        """
        Logs trade events for audit trail and sends to Performance Analysis Agent
        and AI Training Agent.
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "order_id": order_id,
            "event_type": event_type,
            "details": details,
            "local_order_state": self.order_state_manager.get_order(order_id)
        }
        logger.info(f"📝 TRADE EVENT: {json.dumps(log_entry)}")

        # Send to Performance Analysis Agent
        if self.performance_agent:
            await self.performance_agent.process_trade_event(log_entry)
        
        # Send to AI Training Agent
        if self.ai_training_agent:
            await self.ai_training_agent.feed_live_feedback(log_entry)

    async def _monitor_positions(self):
        """Monitor current positions and track Live Trade P&L"""
        while self.is_running:
            try:
                logger.info("💰 Monitoring positions and P&L...")
                
                positions = []
                if not self.dry_run and self.smartapi_client:
                    response = await asyncio.get_running_loop().run_in_executor(None, self.smartapi_client.getPositions) # Make API call async
                    if response and response.get('status'):
                        positions = response['data']['netPositions']
                        logger.info(f"📊 Retrieved {len(positions)} positions from SmartAPI.")
                    else:
                        logger.warning(f"⚠️ Failed to get positions from SmartAPI: {response.get('message', 'N/A')}")
                elif self.dry_run:
                    # Simulate positions from active_orders
                    for order_id, order_data in self.order_state_manager.get_all_orders().items():
                        if order_data['status'] == OrderStatus.COMPLETE and order_data['filled_qty'] > 0 and not order_data.get('exit_time'):
                            positions.append({
                                "tradingsymbol": order_data['tradingsymbol'],
                                "netqty": order_data['filled_qty'] if order_data['transaction_type'] == 'BUY' else -order_data['filled_qty'],
                                "buyavgprice": order_data['avg_price'] if order_data['transaction_type'] == 'BUY' else 0,
                                "sellavgprice": order_data['avg_price'] if order_data['transaction_type'] == 'SELL' else 0,
                                "ltp": order_data['avg_price'] * 1.05, # Simulate some profit
                                "pnl": 0 # Will calculate below
                            })

                for pos in positions:
                    trading_symbol = pos.get('tradingsymbol')
                    net_qty = int(pos.get('netqty', 0))
                    buy_avg_price = float(pos.get('buyavgprice', 0))
                    sell_avg_price = float(pos.get('sellavgprice', 0))
                    
                    ltp = None
                    if self.market_data_agent:
                        ltp = await self.market_data_agent.get_ltp(trading_symbol)
                    if ltp is None:
                        logger.warning(f"⚠️ Could not fetch real-time LTP for {trading_symbol}. Using placeholder for P&L calculation.")
                        ltp = pos.get('ltp', 0) # Fallback to API provided LTP or 0

                    if net_qty == 0:
                        continue # No open position

                    entry_price = buy_avg_price if net_qty > 0 else sell_avg_price
                    
                    # Calculate Unrealized P&L
                    unrealized_pnl = (ltp - entry_price) * abs(net_qty)
                    if net_qty < 0: # Short position
                        unrealized_pnl = -unrealized_pnl

                    # Find the corresponding original signal if available
                    original_signal = None
                    order_id_for_pnl = 'N/A'
                    for order_data in self.order_state_manager.get_all_orders().values():
                        if order_data.get('tradingsymbol') == trading_symbol and order_data.get('status') == OrderStatus.COMPLETE:
                            original_signal = order_data.get('signal_data')
                            order_id_for_pnl = order_data.get('order_id')
                            break

                    pnl_report = {
                        "order_id": order_id_for_pnl, # Include order_id for tracking
                        "tradingsymbol": trading_symbol,
                        "status": "open",
                        "entry_price": entry_price,
                        "ltp": ltp,
                        "pnl": round(unrealized_pnl, 2),
                        "net_quantity": net_qty,
                        "strategy_id": original_signal.get('strategy_id') if original_signal else "N/A",
                        "confidence": original_signal.get('confidence') if original_signal else "N/A",
                        "stoploss_status": "ACTIVE" if self.order_state_manager.get_order(order_id_for_pnl, {}).get('sl_order_id') else "N/A",
                        "target_status": "ACTIVE" if self.order_state_manager.get_order(order_id_for_pnl, {}).get('tp_order_id') else "N/A"
                    }
                    logger.info(f"📈 Live P&L: {json.dumps(pnl_report)}")
                    await self._send_notification(f"📈 Live P&L for {trading_symbol}: {pnl_report['pnl']} (LTP: {ltp})", llm_readable=True, pnl_data=pnl_report)

                await asyncio.sleep(30)
            except Exception as e:
                logger.error(f"❌ [ERROR] Position monitoring failed: {e}")

    async def _calculate_execution_quality(self):
        """Calculate execution quality metrics: slippage, latency"""
        while self.is_running:
            try:
                logger.info("⏱️ Calculating execution quality...")
                for order_id, order_data in self.order_state_manager.get_all_orders().items():
                    if order_data['status'] == OrderStatus.COMPLETE and order_data['filled_qty'] > 0:
                        signal_price = order_data['signal_data'].get('entry_price')
                        executed_price = order_data['avg_price']
                        
                        if signal_price and executed_price:
                            slippage_abs = abs(executed_price - signal_price)
                            slippage_pct = (slippage_abs / signal_price) * 100 if signal_price else 0
                            logger.info(f"📊 Order {order_id} - Signal Price: {signal_price}, Executed Price: {executed_price}, Slippage: {slippage_pct:.2f}%")

                            if slippage_pct > (self.config.execution.max_slippage * 100):
                                logger.warning(f"⚠️ High slippage detected for order {order_id}: {slippage_pct:.2f}% (Max allowed: {self.config.execution.max_slippage*100:.2f}%)")

                        signal_time_str = order_data['signal_data'].get('timestamp')
                        if signal_time_str:
                            try:
                                signal_time = datetime.fromisoformat(signal_time_str)
                                execution_time = datetime.fromisoformat(order_data['entry_time'])
                                latency_seconds = (execution_time - signal_time).total_seconds()
                                logger.info(f"⏱️ Order {order_id} - Latency (Signal to Execution): {latency_seconds:.2f} seconds")
                            except ValueError:
                                logger.warning(f"⚠️ Could not parse signal timestamp for order {order_id}: {signal_time_str}")
                        else:
                            logger.warning(f"⚠️ No signal timestamp found for order {order_id}.")

                await asyncio.sleep(60)
            except Exception as e:
                logger.error(f"❌ [ERROR] Execution quality calculation failed: {e}")

    async def _get_real_ltp(self, tradingsymbol: str) -> Optional[float]:
        """Get real Last Traded Price for a trading symbol from Market Monitoring Agent."""
        if self.market_data_agent:
            return await self.market_data_agent.get_ltp(tradingsymbol)
        else:
            logger.error("❌ MarketMonitoringAgent not initialized. Cannot get real LTP.")
            return None

    def _get_token_from_symbol(self, tradingsymbol: str) -> Optional[str]:
        """Get token from trading symbol using InstrumentManager."""
        if not self.instrument_manager:
            logger.error("InstrumentManager not initialized. Cannot get token from symbol.")
            return None
        return self.instrument_manager.get_token_by_symbol(tradingsymbol)

    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("🧹 [CLEANUP] Cleaning up Options Execution Agent...")
            self.is_running = False
            
            if hasattr(self, 'heartbeat') and self.heartbeat:
                self.heartbeat.stop_heartbeat()

            await self._save_active_orders()
            if self.token_manager:
                await self.token_manager.stop_token_refresh_loop()
                logger.info("🔑 SmartAPI client resources cleaned up.")
            logger.info("✅ [SUCCESS] Options Execution Agent cleaned up")
        except Exception as e:
            logger.error(f"❌ [ERROR] Cleanup failed: {e}")

    async def start(self):
        """Starts the execution agent's monitoring loops."""
        self.is_running = True
        logger.info("▶️ Options Execution Agent started. Monitoring signals, orders, and positions...")
        await self._start_background_tasks()

    async def stop(self):
        """Stops the execution agent's monitoring loops."""
        logger.info("🛑 Stopping Options Execution Agent...")
        self.is_running = False
        
        # Corrected: Remove redundant heartbeat initialization
        if hasattr(self, 'heartbeat') and self.heartbeat:
            self.heartbeat.stop_heartbeat()
        await self.cleanup()

    async def _start_background_tasks(self):
        """Starts all concurrent background tasks."""
        await asyncio.gather(
            self._monitor_signals(),
            self._manage_orders(),
            self._monitor_positions(),
            self._calculate_execution_quality(),
            self._monitor_sl_tp(),
            self._eod_square_off()
        )

    async def _reconstruct_active_orders_from_api(self):
        """
        Recovers from an incomplete state by fetching the order book on restart
        and reconstructing the local shadow order book using OrderStateManager.
        This is primarily for orders placed *before* the agent started,
        complementing the persistence file.
        """
        logger.info("🔄 Reconstructing active orders from SmartAPI order book...")
        try:
            order_book = await self.get_order_book() # This is an API call, assumed async
            for order in order_book:
                order_id = order.get('orderid')
                if order_id:
                    existing_order = self.order_state_manager.get_order(order_id)
                    
                    api_status = order.get('status', 'UNKNOWN').upper()
                    new_status = getattr(OrderStatus, api_status, OrderStatus.UNKNOWN_EXTERNAL_STATUS)

                    if existing_order:
                        self.order_state_manager.update_order_status(order_id, new_status, {
                            "filled_qty": order.get('filledquantity', 0),
                            "avg_price": order.get('averageprice', 0),
                            "last_update_time": datetime.now().isoformat(),
                        })
                        logger.info(f"✅ Updated existing order {order_id} from SmartAPI with status {new_status.name}")
                    elif new_status not in [OrderStatus.COMPLETE, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                        self.order_state_manager.add_order(order_id, {
                            "order_id": order_id,
                            "status": new_status,
                            "filled_qty": order.get('filledquantity', 0),
                            "avg_price": order.get('averageprice', 0),
                            "signal_data": {},
                            "entry_time": datetime.now().isoformat(),
                            "last_update_time": datetime.now().isoformat(),
                            "transaction_type": order.get('transactiontype'),
                            "tradingsymbol": order.get('tradingsymbol'),
                            "quantity": order.get('quantity'),
                            "price": order.get('price'),
                            "order_type": order.get('ordertype'),
                            "product": order.get('producttype'),
                            "exchange": order.get('exchange'),
                            "variety": order.get('variety'),
                            "retries": 0
                        })
                        logger.info(f"✅ Recovered new active order from SmartAPI: {order_id} with status {new_status.name}")
            logger.info(f"📊 Reconstructed {len(self.order_state_manager.get_all_orders())} active orders (combined with persisted).")
            await self._save_active_orders()
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to reconstruct active orders from SmartAPI: {e}")

    async def _save_active_orders(self):
        """Saves the current state of orders from OrderStateManager to a Parquet file."""
        try:
            all_orders = self.order_state_manager.get_all_orders()
            if not all_orders:
                if self.persistence_file.exists():
                    loop = asyncio.get_running_loop()
                    await loop.run_in_executor(None, self.persistence_file.unlink) # Make file operation async
                    logger.info("🗑️ Deleted active orders persistence file as no active orders remain.")
                return

            orders_list = []
            for order_id, order_data in all_orders.items():
                flat_order_data = order_data.copy()
                if isinstance(flat_order_data.get('status'), OrderStatus):
                    flat_order_data['status'] = flat_order_data['status'].name
                
                if 'signal_data' in flat_order_data and isinstance(flat_order_data['signal_data'], dict):
                    flat_order_data['signal_data_json'] = json.dumps(flat_order_data['signal_data'])
                    del flat_order_data['signal_data']
                
                orders_list.append(flat_order_data)

            df = pl.DataFrame(orders_list)
            self.persistence_file.parent.mkdir(parents=True, exist_ok=True)
            loop = asyncio.get_running_loop()
            await loop.run_in_executor(None, df.write_parquet, self.persistence_file) # Make file operation async
            logger.info(f"💾 Active orders saved to {self.persistence_file}")
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to save active orders to persistence file: {e}")

    async def _load_active_orders(self):
        """Loads active orders from a Parquet file on initialization into OrderStateManager."""
        try:
            if self.persistence_file.exists():
                loop = asyncio.get_running_loop()
                df = await loop.run_in_executor(None, pl.read_parquet, self.persistence_file) # Make file operation async
                loaded_orders_list = df.to_dicts()
                
                reconstructed_orders: Dict[str, Dict[str, Any]] = {}
                for order_data in loaded_orders_list:
                    order_id = order_data.get('order_id')
                    if order_id:
                        if 'signal_data_json' in order_data and order_data['signal_data_json']:
                            try:
                                order_data['signal_data'] = json.loads(order_data['signal_data_json'])
                            except json.JSONDecodeError:
                                logger.warning(f"Could not decode signal_data_json for order {order_id}. Storing as empty dict.")
                                order_data['signal_data'] = {}
                            del order_data['signal_data_json']
                        else:
                            order_data['signal_data'] = {}

                        if 'status' in order_data and isinstance(order_data['status'], str):
                            try:
                                order_data['status'] = OrderStatus[order_data['status']]
                            except KeyError:
                                logger.warning(f"Unknown status '{order_data['status']}' for order {order_id}. Setting to UNKNOWN_EXTERNAL_STATUS.")
                                order_data['status'] = OrderStatus.UNKNOWN_EXTERNAL_STATUS
                        
                        reconstructed_orders[order_id] = order_data
                
                self.order_state_manager.set_orders(reconstructed_orders)
                logger.info(f"✅ Loaded {len(reconstructed_orders)} active orders from {self.persistence_file} into OrderStateManager.")
            else:
                logger.info("ℹ️ No existing active orders persistence file found.")
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to load active orders from persistence file: {e}")
            self.order_state_manager.set_orders({})

    async def _place_stoploss_order(self, original_order_id: str, sl_price: float, tradingsymbol: str, quantity: int, transaction_type: str) -> Optional[str]:
        """Places a stop-loss order."""
        sl_transaction_type = "SELL" if transaction_type == "BUY" else "BUY"
        order_params = {
            "tradingsymbol": tradingsymbol,
            "transaction_type": sl_transaction_type,
            "exchange": "NFO",
            "order_type": "SL",
            "quantity": quantity,
            "price": sl_price,
            "triggerprice": sl_price
        }
        logger.info(f"Attempting to place SL order for {original_order_id}: {order_params}")
        sl_order_id = await self.place_order(order_params)
        if sl_order_id:
            self.order_state_manager.update_order_details(original_order_id, {'sl_order_id': sl_order_id})
            # The SL order itself will be added to order_state_manager by place_order
            logger.info(f"✅ SL order {sl_order_id} placed for {original_order_id}.")
            await self._save_active_orders()
        return sl_order_id

    async def _place_target_order(self, original_order_id: str, target_price: float, tradingsymbol: str, quantity: int, transaction_type: str) -> Optional[str]:
        """Places a target profit order."""
        tp_transaction_type = "SELL" if transaction_type == "BUY" else "BUY"
        order_params = {
            "tradingsymbol": tradingsymbol,
            "transaction_type": tp_transaction_type,
            "exchange": "NFO",
            "order_type": "LIMIT",
            "quantity": quantity,
            "price": target_price
        }
        logger.info(f"Attempting to place TP order for {original_order_id}: {order_params}")
        tp_order_id = await self.place_order(order_params)
        if tp_order_id:
            self.order_state_manager.update_order_details(original_order_id, {'tp_order_id': tp_order_id})
            # The TP order itself will be added to order_state_manager by place_order
            logger.info(f"✅ TP order {tp_order_id} placed for {original_order_id}.")
            await self._save_active_orders()
        return tp_order_id

    async def _monitor_sl_tp(self):
        """Monitors real-time price to trigger SL/TP and cancels unfilled legs."""
        while self.is_running:
            try:
                logger.info("🛡️ Monitoring Stop Loss and Target Profit levels...")
                
                for order_id, order_data in list(self.order_state_manager.get_all_orders().items()):
                    if order_data['status'] == OrderStatus.COMPLETE and order_data.get('signal_data'):
                        signal = order_data['signal_data']
                        tradingsymbol = order_data['tradingsymbol']
                        quantity = order_data['filled_qty']
                        transaction_type = order_data['transaction_type']
                        
                        ltp = await self._get_real_ltp(tradingsymbol)
                        if ltp is None:
                            logger.error(f"❌ [ERROR] Cannot get real LTP for {tradingsymbol}. Skipping SL/TP monitoring.")
                            continue

                        if signal.get('stoploss') and not order_data.get('sl_order_id'):
                            await self._place_stoploss_order(order_id, signal['stoploss'], tradingsymbol, quantity, transaction_type)
                        
                        if signal.get('target') and not order_data.get('tp_order_id'):
                            await self._place_target_order(order_id, signal['target'], tradingsymbol, quantity, transaction_type)

                        sl_order_id = order_data.get('sl_order_id')
                        tp_order_id = order_data.get('tp_order_id')

                        if sl_order_id:
                            sl_order_status = self.order_state_manager.get_order(sl_order_id, {}).get('status')
                            if sl_order_status == OrderStatus.COMPLETE:
                                logger.warning(f"🛑 Stop Loss hit for {tradingsymbol} (Order {order_id}).")
                                if tp_order_id and self.order_state_manager.get_order(tp_order_id, {}).get('status') not in [OrderStatus.COMPLETE, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                                    logger.info(f"Cancelling pending TP order {tp_order_id} for {order_id}.")
                                    await self.cancel_order(tp_order_id)
                                self.order_state_manager.update_order_status(order_id, OrderStatus.EXITED_SL)
                                self.order_state_manager.update_order_details(order_id, {
                                    'exit_time': datetime.now().isoformat(),
                                    'exit_price': self.order_state_manager.get_order(sl_order_id)['avg_price']
                                })
                                await self._send_notification(f"🛑 SL Hit: {tradingsymbol} exited at {self.order_state_manager.get_order(sl_order_id)['avg_price']}.")
                                await self._save_active_orders()

                        if tp_order_id:
                            tp_order_status = self.order_state_manager.get_order(tp_order_id, {}).get('status')
                            if tp_order_status == OrderStatus.COMPLETE:
                                logger.warning(f"🎯 Target Profit hit for {tradingsymbol} (Order {order_id}).")
                                if sl_order_id and self.order_state_manager.get_order(sl_order_id, {}).get('status') not in [OrderStatus.COMPLETE, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                                    logger.info(f"Cancelling pending SL order {sl_order_id} for {order_id}.")
                                    await self.cancel_order(sl_order_id)
                                self.order_state_manager.update_order_status(order_id, OrderStatus.EXITED_TP)
                                self.order_state_manager.update_order_details(order_id, {
                                    'exit_time': datetime.now().isoformat(),
                                    'exit_price': self.order_state_manager.get_order(tp_order_id)['avg_price']
                                })
                                await self._send_notification(f"🎯 TP Hit: {tradingsymbol} exited at {self.order_state_manager.get_order(tp_order_id)['avg_price']}.")
                                await self._save_active_orders()

                        if signal.get('trailing_stoploss') and ltp:
                            await self._update_trailing_stoploss(order_id, ltp, signal['trailing_stoploss'])

                await asyncio.sleep(5)
            except Exception as e:
                logger.error(f"❌ [ERROR] SL/TP monitoring failed: {e}")
                await asyncio.sleep(10)

    async def _update_trailing_stoploss(self, order_id: str, current_ltp: float, trailing_stoploss_percentage: float):
        """
        Updates the trailing stop-loss price for an open position.
        """
        logger.info(f"📈 Updating trailing stop-loss for order {order_id} with LTP {current_ltp}...")
        
        order_data = self.order_state_manager.get_order(order_id)
        if not order_data or not order_data.get('sl_order_id'):
            logger.warning(f"⚠️ No active order or SL order found for {order_id} to trail.")
            return

        sl_order_id = order_data['sl_order_id']
        sl_order_details = self.order_state_manager.get_order(sl_order_id)

        if not sl_order_details:
            logger.warning(f"⚠️ SL order details not found for SL order ID {sl_order_id}.")
            return

        current_sl_price = sl_order_details['price']
        transaction_type = order_data['transaction_type']

        new_sl_price = 0.0
        if transaction_type == 'BUY':
            new_sl_price = current_ltp * (1 - trailing_stoploss_percentage)
            if new_sl_price > current_sl_price:
                logger.info(f"⬆️ Trailing SL for BUY order {order_id}: Old SL {current_sl_price:.2f}, New SL {new_sl_price:.2f}")
                await self.modify_order(sl_order_id, new_price=new_sl_price)
                self.order_state_manager.update_order_details(order_id, {'sl_price': new_sl_price})
                await self._send_notification(f"📈 Trailing SL for {order_data['tradingsymbol']} updated to ₹{new_sl_price:.2f} (LTP: {current_ltp}).")
                await self._log_trade_event(order_id, "TRAILING_SL_UPDATE", {"ltp": current_ltp, "trailing_pct": trailing_stoploss_percentage, "new_sl": new_sl_price})
                await self._save_active_orders()
            else:
                logger.info(f"ℹ️ New SL price {new_sl_price:.2f} not higher than current SL {current_sl_price:.2f} for BUY order {order_id}. No update.")
        elif transaction_type == 'SELL':
            new_sl_price = current_ltp * (1 + trailing_stoploss_percentage)
            if new_sl_price < current_sl_price:
                logger.info(f"⬇️ Trailing SL for SELL order {order_id}: Old SL {current_sl_price:.2f}, New SL {new_sl_price:.2f}")
                await self.modify_order(sl_order_id, new_price=new_sl_price)
                self.order_state_manager.update_order_details(order_id, {'sl_price': new_sl_price})
                await self._send_notification(f"📈 Trailing SL for {order_data['tradingsymbol']} updated to ₹{new_sl_price:.2f} (LTP: {current_ltp}).")
                await self._log_trade_event(order_id, "TRAILING_SL_UPDATE", {"ltp": current_ltp, "trailing_pct": trailing_stoploss_percentage, "new_sl": new_sl_price})
                await self._save_active_orders()
            else:
                logger.info(f"ℹ️ New SL price {new_sl_price:.2f} not lower than current SL {current_sl_price:.2f} for SELL order {order_id}. No update.")

    async def _eod_square_off(self):
        """Performs end-of-day auto square-off for all open positions."""
        while self.is_running:
            now = datetime.now()
            if now >= self.eod_square_off_time and now < self.eod_square_off_time + timedelta(minutes=5):
                logger.info("⏰ Initiating End-of-Day Square-Off...")
                positions_to_square_off = []
                if not self.dry_run and self.smartapi_client:
                    response = await asyncio.get_running_loop().run_in_executor(None, self.smartapi_client.getPositions) # Make API call async
                    if response and response.get('status'):
                        positions_to_square_off = [p for p in response['data']['netPositions'] if int(p.get('netqty', 0)) != 0]
                elif self.dry_run:
                    for order_id, order_data in self.order_state_manager.get_all_orders().items():
                        if order_data['status'] == OrderStatus.COMPLETE and order_data['filled_qty'] > 0 and not order_data.get('exit_time'):
                            positions_to_square_off.append({
                                "tradingsymbol": order_data['tradingsymbol'],
                                "netqty": order_data['filled_qty'] if order_data['transaction_type'] == 'BUY' else -order_data['filled_qty'],
                                "producttype": order_data['product']
                            })

                for pos in positions_to_square_off:
                    tradingsymbol = pos['tradingsymbol']
                    net_qty = int(pos['netqty'])
                    product_type = pos['producttype']
                    
                    if net_qty != 0:
                        transaction_type = "SELL" if net_qty > 0 else "BUY"
                        quantity = abs(net_qty)
                        
                        logger.info(f"SQUARING OFF: {transaction_type} {quantity} of {tradingsymbol} ({product_type})")
                        square_off_params = {
                            "tradingsymbol": tradingsymbol,
                            "transaction_type": transaction_type,
                            "exchange": "NFO",
                            "order_type": "MARKET",
                            "quantity": quantity,
                            "product": product_type,
                            "variety": "NORMAL"
                        }
                        square_off_order_id = await self.place_order(square_off_params)
                        if square_off_order_id:
                            logger.info(f"✅ Square-off order placed for {tradingsymbol}. Order ID: {square_off_order_id}")
                            await self._send_notification(f"⏰ EOD Square-Off: {tradingsymbol} ({quantity} qty) exited.")
                            for order_id, order_data in self.order_state_manager.get_all_orders().items():
                                if order_data.get('tradingsymbol') == tradingsymbol and order_data.get('status') == OrderStatus.COMPLETE:
                                    self.order_state_manager.update_order_status(order_id, OrderStatus.EXITED_EOD)
                                    self.order_state_manager.update_order_details(order_id, {'exit_time': datetime.now().isoformat()})
                                    break
                            await self._save_active_orders()
                        else:
                            logger.error(f"❌ Failed to place square-off order for {tradingsymbol}.")
                
                await asyncio.sleep(3600)
            else:
                await asyncio.sleep(60)

    async def _send_notification(self, message: str, llm_readable: bool = False, pnl_data: Optional[Dict[str, Any]] = None):
        """
        Sends notifications to various channels via the AlertingNotificationsSystem agent.
        If llm_readable is True, formats for LLM Interface Agent.
        If pnl_data is provided, formats for UI/Telegram/Dashboard.
        """
        logger.info(f"🔔 NOTIFICATION: {message}")
        
        if self.alerting_agent:
            await self.alerting_agent.send_notification(message, llm_readable=llm_readable, pnl_data=pnl_data)
        else:
            logger.warning("⚠️ AlertingNotificationsSystem agent not initialized. Cannot send centralized notifications.")
            if llm_readable:
                if pnl_data:
                    llm_message = (
                        f"Executed {pnl_data.get('net_quantity')} lots of {pnl_data.get('tradingsymbol')} "
                        f"at ₹{pnl_data.get('entry_price')} with SL ₹{pnl_data.get('signal_data', {}).get('stoploss', 'N/A')} "
                        f"and target ₹{pnl_data.get('signal_data', {}).get('target', 'N/A')}. "
                        f"Trade confidence: {pnl_data.get('confidence', 'N/A') * 100:.0f}% "
                        f"(Strategy {pnl_data.get('strategy_id', 'N/A')}). "
                        f"Current P&L: ₹{pnl_data.get('pnl')}."
                    )
                else:
                    llm_message = message
                logger.info(f"🗣️ LLM Interface Message (fallback): {llm_message}")

# Example usage
async def main():
    agent = OptionsExecutionAgent(dry_run=True) 
    try:
        await agent.initialize()
        sample_signal = {
            "underlying": "BANKNIFTY",
            "strike_price": 47800,
            "option_type": "CE",
            "expiry": "2025-07-25",
            "action": "BUY",
            "entry_price": 142,
            "stoploss": 122,
            "target": 185,
            "lot_size": 2,
            "strategy_id": "strat_012",
            "confidence": 0.83,
            "timestamp": datetime.now().isoformat(),
            "execution_algorithm": "market" # Specify execution algorithm
        }
        logger.info("Simulating a signal for processing...")
        order_id = await agent._process_signal(sample_signal)
        if order_id:
            logger.info(f"Simulated signal processed, order ID: {order_id}")
        else:
            logger.warning("Simulated signal processing failed.")

        await agent.start()
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user 🛑")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
