#!/usr/bin/env python3
"""
Options Risk Management Agent - Comprehensive Options Risk Control
"""

import asyncio
import logging
from utils.heartbeat import create_heartbeat
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, TYPE_CHECKING

if TYPE_CHECKING:
    from agents.risk_management.risk_evaluator import RiskEvaluator
    from agents.risk_management.monitoring import Monitoring
    from agents.risk_management.risk_persistence import RiskPersistenceManager
    from agents.risk_management.trade_manager import TradeManager
    from agents.risk_management.dynamic_adjustments import DynamicAdjustments
import json
from collections import deque

from agents.risk_management.config_manager import ConfigManager
from agents.risk_management.state_manager import StateManager, AgentState
from agents.risk_management.signal_receiver import SignalReceiver
from agents.options_market_monitoring_agent import OptionsMarketMonitoringAgent
# Lazy loaded imports:
# from agents.risk_management.risk_evaluator import RiskEvaluator
# from agents.risk_management.monitoring import Monitoring
# from agents.risk_management.risk_persistence import RiskPersistenceManager
# from agents.risk_management.trade_manager import TradeManager
# from agents.risk_management.dynamic_adjustments import DynamicAdjustments
from agents.risk_management.constants import (
    DEFAULT_INITIAL_CAPITAL,
    DEFAULT_LOT_SIZE,
    DEFAULT_RISK_SCORE,
)

logger = logging.getLogger(__name__)

class OptionsRiskManagementAgent:
    """Options Risk Management Agent for comprehensive risk control"""
    
    def __init__(self, config_path: str = "config/options_risk_management_config.yaml"):
        self.config_manager = ConfigManager(config_path)
        self.state_manager = StateManager()
        
        self.heartbeat = create_heartbeat('risk_management')
        self.agent_id = f"RiskAgent_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.total_capital: float = 0.0
        self.current_capital: float = 0.0
        self.daily_high_capital: float = 0.0
        self.daily_drawdown_loss: float = 0.0
        self.daily_drawdown_pct: float = 0.0
        self.trading_paused: bool = False
        self.pause_cool_off_until: Optional[datetime] = None
        self.daily_capital_used: float = 0.0
        self.daily_trades_count: int = 0
        self.monitoring_tasks: List[asyncio.Task] = []

        # Lazy loaded components
        self._risk_evaluator: Optional['RiskEvaluator'] = None
        self._monitoring: Optional['Monitoring'] = None
        self._risk_persistence_manager: Optional['RiskPersistenceManager'] = None
        self._trade_manager: Optional['TradeManager'] = None
        self._dynamic_adjustments: Optional['DynamicAdjustments'] = None

        # Initialize Market Monitoring Agent
        self.market_monitoring_agent = OptionsMarketMonitoringAgent()

        # Initialize Signal Receiver
        signal_receiver_config = {
            'enable_websocket': self.config_manager.get('signal_receiver', {}).get('enable_websocket', False),
            'websocket_uri': self.config_manager.get('signal_receiver', {}).get('websocket_uri', 'ws://localhost:8765'),
            'file_monitor_interval': self.config_manager.get('signal_receiver', {}).get('file_monitor_interval', 5)
        }
        self.signal_receiver = SignalReceiver(signal_receiver_config, self._process_received_signal)

        # Signal processing queue
        self.pending_signals = asyncio.Queue(maxsize=50)

        logger.info("🛡️ [INIT] Options Risk Management Agent initialized")
    
    @property
    def risk_evaluator(self) -> 'RiskEvaluator':
        """Lazily loads and returns the RiskEvaluator instance."""
        if self._risk_evaluator is None:
            from agents.risk_management.risk_evaluator import RiskEvaluator
            self._risk_evaluator = RiskEvaluator(self.config_manager.config, self)
        return self._risk_evaluator

    @property
    def monitoring(self) -> 'Monitoring':
        """Lazily loads and returns the Monitoring instance."""
        if self._monitoring is None:
            from agents.risk_management.monitoring import Monitoring
            self._monitoring = Monitoring(self.config_manager.config, self, self._send_alert)
        return self._monitoring

    @property
    def risk_persistence_manager(self) -> 'RiskPersistenceManager':
        """Lazily loads and returns the RiskPersistenceManager instance."""
        if self._risk_persistence_manager is None:
            from agents.risk_management.risk_persistence import RiskPersistenceManager
            self._risk_persistence_manager = RiskPersistenceManager()
        return self._risk_persistence_manager

    @property
    def trade_manager(self) -> 'TradeManager':
        """Lazily loads and returns the TradeManager instance."""
        if self._trade_manager is None:
            from agents.risk_management.trade_manager import TradeManager
            # Note: emergency_safeguard_trigger needs to be bound or passed correctly.
            # Assuming it's a method of this class, it should be accessible.
            self._trade_manager = TradeManager(self.config_manager, self.state_manager, self.emergency_safeguard_trigger)
        return self._trade_manager

    @property
    def dynamic_adjustments(self) -> 'DynamicAdjustments':
        """Lazily loads and returns the DynamicAdjustments instance."""
        if self._dynamic_adjustments is None:
            from agents.risk_management.dynamic_adjustments import DynamicAdjustments
            self._dynamic_adjustments = DynamicAdjustments()
        return self._dynamic_adjustments

    async def initialize(self, **kwargs):
        """Initialize the agent with enhanced exception handling and logging."""
        self.state_manager.set_state(AgentState.INITIALIZING)
        try:
            logger.info("⚙️ [INIT] Loading configuration...")
            self.config_manager.load_config()
            
            logger.info("⚙️ [INIT] Setting initial capital and state...")
            self.total_capital = self.config_manager.get('initial_capital', DEFAULT_INITIAL_CAPITAL)
            self.current_capital = self.total_capital
            self.daily_high_capital = self.total_capital
            
            logger.info("⚙️ [INIT] Starting heartbeat...")
            self.heartbeat.start_heartbeat()
            
            # Initialize TradeManager with current capital and agent ID
            self.trade_manager.set_agent_capital_and_id(self.current_capital, self.agent_id)
            
            logger.info("⚙️ [INIT] Initializing Market Monitoring Agent...")
            await self.market_monitoring_agent.initialize()

            logger.info("⚙️ [INIT] Starting Signal Receiver...")
            # Start signal receiver as a background task
            asyncio.create_task(self.signal_receiver.start())

            self.state_manager.set_state(AgentState.RUNNING)
            logger.info(f"✅ [SUCCESS] {self.agent_id} initialized successfully. Current capital: {self.current_capital:,.2f}")
            return True
        except FileNotFoundError as e:
            logger.error(f"❌ [INITIALIZE ERROR] Configuration file not found: {e}. Please ensure the config file exists at the specified path.", exc_info=True)
            self.state_manager.set_state(AgentState.STOPPED)
            return False
        except KeyError as e:
            logger.error(f"❌ [INITIALIZE ERROR] Missing expected key in configuration file: {e}. Please check your config file for completeness.", exc_info=True)
            self.state_manager.set_state(AgentState.STOPPED)
            return False
        except OSError as e:
            logger.error(f"❌ [INITIALIZE ERROR] OS error occurred during initialization (e.g., permissions): {e}. Ensure the agent has necessary file system permissions.", exc_info=True)
            self.state_manager.set_state(AgentState.STOPPED)
            return False
        except Exception as e:
            logger.error(f"❌ [INITIALIZE ERROR] An unexpected error occurred during agent initialization: {e}", exc_info=True)
            self.state_manager.set_state(AgentState.STOPPED)
            return False
    
    async def start(self, **kwargs) -> bool:
        """Start the risk management agent with enhanced exception handling and logging."""
        if not self.state_manager.is_running():
            logger.warning("Agent is not running. Please call initialize() first before starting.")
            return False
            
        try:
            logger.info("🚀 [START] Starting Options Risk Management Agent...")
            await self._reset_daily_metrics()

            self.trading_mode = kwargs.get('trading_mode', 'real')
            self.virtual_account = kwargs.get('virtual_account')

            logger.info(f"🛡️ [MODE] Risk management mode set to: {self.trading_mode.upper()}")
            if self.virtual_account:
                logger.info(f"🗄️ [VIRTUAL] Using virtual account: {self.virtual_account}")

            logger.info("🚀 [START] Launching monitoring tasks...")
            self.monitoring_tasks = [
                asyncio.create_task(self.monitoring.monitor_daily_drawdown()),
                asyncio.create_task(self.monitoring.monitor_portfolio_exposure()),
                # TODO: Implement full Greek monitoring logic in Monitoring class
                asyncio.create_task(self.monitoring.monitor_greeks_exposure()),
                # TODO: Implement full alert generation logic in Monitoring class
                asyncio.create_task(self.monitoring.generate_risk_alerts()),
                asyncio.create_task(self.monitoring.broadcast_risk_summary()),
                asyncio.create_task(self._monitor_active_positions()),
                asyncio.create_task(self._process_pending_signals())
            ]
            logger.info(f"🚀 [START] Successfully launched {len(self.monitoring_tasks)} monitoring tasks.")

            logger.info("✅ [SUCCESS] Risk Management Agent started successfully.")
            return True
        except asyncio.CancelledError:
            logger.info("Agent start process was cancelled.")
            self.state_manager.set_state(AgentState.STOPPED)
            return False
        except Exception as e:
            logger.error(f"❌ [START ERROR] Failed to start agent: {e}", exc_info=True)
            self.state_manager.set_state(AgentState.STOPPED)
            return False

    async def cleanup(self):
        """
        Cleanup the agent and stop all background tasks with enhanced exception handling and logging.
        """
        self.state_manager.set_state(AgentState.STOPPING)
        try:
            logger.info("🛑 [CLEANUP] Initiating shutdown sequence for Risk Management Agent...")
            
            # Stop signal receiver
            if hasattr(self, 'signal_receiver') and self.signal_receiver:
                logger.debug("Stopping signal receiver...")
                await self.signal_receiver.stop()
                logger.debug("Signal receiver stopped.")

            # Stop heartbeat if it exists
            if hasattr(self, 'heartbeat') and self.heartbeat:
                logger.debug("Stopping heartbeat...")
                self.heartbeat.stop_heartbeat()
                logger.debug("Heartbeat stopped.")

            # Cancel all monitoring tasks
            logger.debug(f"Cancelling {len(self.monitoring_tasks)} monitoring tasks...")
            for task in self.monitoring_tasks:
                if not task.done():
                    task.cancel()
            
            # Wait for tasks to complete cancellation
            if self.monitoring_tasks:
                await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)
                logger.debug("All monitoring tasks have been cancelled and awaited.")

            # Update agent state to stopped
            self.state_manager.set_state(AgentState.STOPPED)
            logger.info("✅ [CLEANUP] Risk Management Agent has been stopped successfully.")
            
        except asyncio.CancelledError:
            # This is expected if cleanup itself is cancelled
            logger.info("Cleanup process was cancelled.")
            self.state_manager.set_state(AgentState.STOPPED) # Ensure state is set to stopped
        except Exception as e:
            # Catch any other unexpected errors during cleanup
            logger.error(f"❌ [CLEANUP ERROR] An unexpected error occurred during agent cleanup: {e}", exc_info=True)
            self.state_manager.set_state(AgentState.STOPPED) # Ensure state is set to stopped even on error
    
    async def _send_alert(self, title: str, message: str):
        """
        Sends an alert to an external system (e.g., Telegram, UI).
        Includes enhanced error handling and logging.
        """
        try:
            # TODO: Integrate with actual alerting systems (e.g., Telegram, Email, Dashboard)
            # For now, we log it and can extend this later.
            full_alert_message = f"🔔 ALERT: {title} - {message}"
            logger.warning(full_alert_message, extra={"alert_title": title, "alert_message": message})
            # Example of potential external integration (commented out)
            # await self.alerting_system.send_notification(title, message)
            logger.debug(f"Alert '{title}' sent successfully.")
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to send alert '{title}': {e}", exc_info=True)
            # Depending on criticality, might want to queue the alert for retry or escalate

    async def _process_received_signal(self, signal_data: Dict[str, Any]):
        """
        Callback method to process signals received from the signal receiver.
        This replaces the dummy signal generation logic.
        """
        try:
            logger.info(f"📨 [SIGNAL] Received signal: {signal_data.get('signal_id', 'N/A')} for {signal_data.get('underlying', 'N/A')}")

            # Convert signal data to the expected format
            processed_signal = await self._convert_signal_format(signal_data)

            # Queue the signal for processing
            try:
                self.pending_signals.put_nowait(processed_signal)
                logger.debug(f"📥 [QUEUE] Signal queued for processing: {processed_signal.get('signal_id')}")
            except asyncio.QueueFull:
                logger.warning("⚠️ [QUEUE] Pending signals queue is full, dropping oldest signal")
                try:
                    self.pending_signals.get_nowait()
                    self.pending_signals.put_nowait(processed_signal)
                except asyncio.QueueEmpty:
                    pass

        except Exception as e:
            logger.error(f"❌ [SIGNAL_PROCESS] Error processing received signal: {e}", exc_info=True)

    async def _convert_signal_format(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert received signal data to the internal format expected by the risk evaluator.
        """
        try:
            # Map common field names
            symbol_mapping = {
                'underlying': signal_data.get('underlying', signal_data.get('symbol', 'NIFTY')),
                'symbol': signal_data.get('underlying', signal_data.get('symbol', 'NIFTY'))
            }

            # Convert to internal format
            converted_signal = {
                "signal_id": signal_data.get('signal_id', f"SIG_{datetime.now().strftime('%H%M%S')}"),
                "symbol": symbol_mapping['symbol'],
                "underlying": symbol_mapping['underlying'],
                "option_type": signal_data.get('option_type', 'call').lower(),
                "strike_price": float(signal_data.get('strike_price', 0)),
                "expiry_date": signal_data.get('expiry', signal_data.get('expiry_date', (datetime.now() + timedelta(days=30)).isoformat())),
                "entry_price": float(signal_data.get('entry_price', 0)),
                "lot_size": int(signal_data.get('lot_size', 1)),
                "stop_loss_price": float(signal_data.get('stoploss', signal_data.get('stop_loss_price', 0))),
                "take_profit_price": float(signal_data.get('target', signal_data.get('take_profit_price', 0))),
                "confidence_score": float(signal_data.get('confidence_score', 0.5)),
                "timeframe": signal_data.get('timeframe', '5min'),
                "action": signal_data.get('action', 'BUY'),
                "timestamp": signal_data.get('timestamp', datetime.now().isoformat()),
                "source": signal_data.get('source', 'signal_generation_agent'),

                # Default Greeks - will be updated with real-time data
                "greeks": signal_data.get('greeks', {"delta": 0.5, "gamma": 0.1, "theta": -0.05, "vega": 0.2}),
                "implied_volatility": float(signal_data.get('implied_volatility', 0.20)),
                "time_to_expiry_days": self._calculate_days_to_expiry(signal_data.get('expiry', signal_data.get('expiry_date'))),
                "trailing_stop_pct": 0.20  # Default 20% trailing stop
            }

            logger.debug(f"🔄 [CONVERT] Converted signal format for {converted_signal['signal_id']}")
            return converted_signal

        except Exception as e:
            logger.error(f"❌ [CONVERT] Error converting signal format: {e}", exc_info=True)
            # Return a minimal valid signal to prevent crashes
            return {
                "signal_id": f"ERROR_{datetime.now().strftime('%H%M%S')}",
                "symbol": "NIFTY",
                "option_type": "call",
                "strike_price": 0,
                "entry_price": 0,
                "lot_size": 0,
                "error": str(e)
            }

    def _calculate_days_to_expiry(self, expiry_str: Optional[str]) -> int:
        """Calculate days to expiry from expiry string"""
        try:
            if not expiry_str:
                return 30  # Default 30 days

            # Parse expiry date
            if 'T' in expiry_str:
                expiry_date = datetime.fromisoformat(expiry_str.replace('Z', '+00:00'))
            else:
                expiry_date = datetime.strptime(expiry_str, '%Y-%m-%d')

            days_to_expiry = (expiry_date - datetime.now()).days
            return max(0, days_to_expiry)

        except Exception as e:
            logger.warning(f"⚠️ [EXPIRY] Error calculating days to expiry: {e}")
            return 30  # Default fallback

    async def evaluate_signal_for_risk(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluates a trading signal for risk, logs the decision, and saves overrides if necessary.
        Includes enhanced exception handling for robustness.
        """
        try:
            decision = await self.risk_evaluator.evaluate_signal_for_risk(signal)
            await self.trade_manager.log_trade_decision(signal, decision)
            
            # Save override if trade is blocked or lot size is adjusted
            if not decision.get('approved', False) or decision.get('adjusted_lot_size', signal.get('lot_size', 1)) != signal.get('lot_size', 1):
                await self._save_risk_override(signal, decision)
            
            return decision
        except Exception as e:
            logger.error(f"❌ [EVALUATE RISK ERROR] Failed to evaluate signal for risk (Signal ID: {signal.get('signal_id', 'N/A')}): {e}", exc_info=True)
            # Return a default decision indicating failure or block
            return {
                'approved': False,
                'downscaled': True,
                'reason': f"Error during risk evaluation: {e}",
                'risk_score': DEFAULT_RISK_SCORE,
                'adjusted_lot_size': 0,
                'llm_explanation': 'Error during risk evaluation.'
            }
    
    async def _save_risk_override(self, signal: Dict[str, Any], risk_decision: Dict[str, Any]):
        """Save risk management override for signal generation agent using RiskPersistenceManager."""
        try:
            await self.risk_persistence_manager.save_risk_override(signal, risk_decision)
        except Exception as e:
            logger.error(f"❌ [SAVE OVERRIDE ERROR] Failed to save risk override for signal {signal.get('signal_id', 'N/A')}: {e}", exc_info=True)

    async def adjust_stop_loss_target(self, trade: Dict[str, Any]) -> Dict[str, Any]:
        """
        Feature 6: Dynamic Stop Loss / Target Adjustment using DynamicAdjustments module.
        """
        # This method would typically interact with the DynamicAdjustments module
        # For now, we'll assume it's a pass-through or a placeholder for future dynamic adjustments.
        # The actual logic for dynamic adjustments would be in dynamic_adjustments.py
        if self._dynamic_adjustments:
            return await self.dynamic_adjustments.adjust_stop_loss_target(trade)
        else:
            logger.warning("DynamicAdjustments module not loaded. Cannot adjust stop loss/target dynamically.")
            return trade # Return original trade if module not available

    async def emergency_safeguard_trigger(self, reason: str):
        """
        Triggers a trading halt and sends shutdown commands.
        """
        logger.critical(f"🚨🚨🚨 [EMERGENCY] Activating emergency safeguard: {reason}! Halting all trading operations.")
        self.state_manager.set_state(AgentState.STOPPING)
        self.trading_paused = True

        logger.critical("🛑 [SHUTDOWN] Sending shutdown command to other agents.")
        
        await self._send_alert("EMERGENCY HALT", f"System-wide trading halt triggered: {reason}")
        
        self.trade_manager.get_trade_history().append({ # Use trade_manager's history
            "timestamp": datetime.now().isoformat(),
            "event_type": "EMERGENCY_HALT",
            "reason": reason
        })

    async def update_trade_status(self, trade_update: Dict[str, Any]):
        """
        Updates the status of an active trade using the TradeManager.
        """
        await self.trade_manager.update_trade_status(trade_update)
        self.current_capital = self.trade_manager.current_capital # Update agent's capital

    async def _get_current_price_from_market_monitoring(self, symbol: str) -> Optional[float]:
        """
        Fetches the current price for a given symbol from the market monitoring agent.
        """
        try:
            # Use the market monitoring agent's get_current_price method
            current_price = self.market_monitoring_agent.get_current_price(symbol, '1min')
            if current_price is not None:
                logger.debug(f"📊 [PRICE] Retrieved current price for {symbol}: {current_price:.2f}")
                return current_price
            else:
                logger.warning(f"⚠️ [PRICE] No current price available for {symbol}")
                return None
        except Exception as e:
            logger.error(f"❌ [MARKET DATA ERROR] Failed to get current price for {symbol} from market monitoring: {e}", exc_info=True)
            return None

    async def _monitor_active_positions(self):
        """
        Monitors active positions for real-time adjustments or alerts.
        Includes enhanced exception handling and logging.
        """
        while self.state_manager.is_running():
            try:
                active_trades = self.trade_manager.get_active_trades()
                if active_trades:
                    logger.info(f"📊 [POSITIONS] Monitoring {len(active_trades)} active trades.")

                    for trade_id, trade_details in list(self.trade_manager.active_trades.items()):
                        # Get current underlying price from market monitoring agent
                        underlying_symbol = trade_details.get('underlying', trade_details.get('symbol'))
                        current_underlying_price = await self._get_current_price_from_market_monitoring(underlying_symbol)

                        if current_underlying_price is None:
                            logger.warning(f"Could not get live underlying price for {underlying_symbol}. Skipping exit condition check for trade {trade_id}.")
                            continue

                        # Update trade with current underlying price for Greeks recalculation
                        trade_details['current_underlying_price'] = current_underlying_price

                        # Estimate current option price based on underlying movement
                        current_option_price = await self._estimate_current_option_price(trade_details, current_underlying_price)
                        if current_option_price:
                            trade_details['current_price'] = current_option_price
                            logger.debug(f"📊 [PRICE_UPDATE] Trade {trade_id}: Underlying={current_underlying_price:.2f}, Option={current_option_price:.2f}")

                        # Update Greeks with current market data
                        await self._update_trade_greeks(trade_details, current_underlying_price)

                        # Check exit conditions using current option price
                        exit_reason = await self.trade_manager.check_exit_conditions(trade_id, current_option_price or trade_details['entry_price'])
                        if exit_reason:
                            # Calculate PnL based on option price movement
                            entry_price = trade_details['entry_price']
                            current_price = current_option_price or entry_price
                            lot_size = trade_details['lot_size']
                            pnl = (current_price - entry_price) * lot_size

                            await self.trade_manager.update_trade_status({
                                "trade_id": trade_id,
                                "status": "closed",
                                "pnl": pnl,
                                "exit_reason": exit_reason,
                                "exit_price": current_price,
                                "underlying_price_at_exit": current_underlying_price
                            })

                            await self._send_alert(
                                f"Trade Exit: {exit_reason}",
                                f"Trade {trade_id} for {underlying_symbol} {trade_details.get('option_type', 'option')} closed at {current_price:.2f} (underlying: {current_underlying_price:.2f}) with PnL: {pnl:.2f}"
                            )

                            logger.info(f"🔚 [TRADE_EXIT] Trade {trade_id} closed: {exit_reason}, PnL: {pnl:.2f}")
                else:
                    logger.debug("📊 [POSITIONS] No active trades to monitor.")
                
                # Sleep for the configured monitoring interval
                await asyncio.sleep(self.config_manager.get('monitoring_interval', 60)) # Default to 60 seconds if not found
            
            except asyncio.CancelledError:
                logger.info("Active positions monitoring task cancelled.")
                break # Exit loop if task is cancelled
            except Exception as e:
                logger.error(f"❌ [MONITOR POSITIONS ERROR] An error occurred while monitoring active positions: {e}", exc_info=True)
                # Continue monitoring even if an error occurs, to avoid bringing down the agent
                await asyncio.sleep(self.config_manager.get('monitoring_interval', 60)) # Wait before retrying

    async def _process_pending_signals(self):
        """
        Process pending signals from the signal receiver queue.
        This replaces the dummy signal generation logic.
        """
        while self.state_manager.is_running():
            try:
                logger.debug("🧠 [SIGNAL_PROCESS] Processing pending signals...")

                # Wait for a signal with timeout
                try:
                    signal = await asyncio.wait_for(
                        self.pending_signals.get(),
                        timeout=5.0
                    )
                except asyncio.TimeoutError:
                    # No signal received, continue
                    continue

                # Skip signals with errors
                if 'error' in signal:
                    logger.warning(f"⚠️ [SIGNAL_PROCESS] Skipping signal with error: {signal.get('error')}")
                    continue

                logger.info(f"⚡ [SIGNAL_PROCESS] Processing signal: {signal['signal_id']} for {signal['symbol']}")

                # Update signal with real-time market data
                await self._enrich_signal_with_market_data(signal)

                # --- Evaluate signal for risk ---
                risk_decision = await self.evaluate_signal_for_risk(signal)

                if risk_decision['approved'] and risk_decision['adjusted_lot_size'] > 0:
                    logger.info(f"✅ [DECISION] Signal {signal['signal_id']} approved. Adjusted lot size: {risk_decision['adjusted_lot_size']}")

                    # Create a new trade based on the approved signal
                    new_trade = {
                        "trade_id": f"TRADE_{signal['signal_id']}",
                        "signal_id": signal['signal_id'],
                        "symbol": signal['symbol'],
                        "underlying": signal.get('underlying', signal['symbol']),
                        "option_type": signal['option_type'],
                        "strike_price": signal['strike_price'],
                        "entry_price": signal['entry_price'],
                        "lot_size": risk_decision['adjusted_lot_size'],
                        "status": "open",
                        "timestamp": datetime.now().isoformat(),
                        "greeks": signal['greeks'],
                        "current_price": signal['entry_price'],  # Initialize current price
                        "confidence_score": signal.get('confidence_score', 0.5),
                        "timeframe": signal.get('timeframe', '5min'),
                        "source": signal.get('source', 'signal_generation_agent')
                    }
                    self.trade_manager.add_active_trade(new_trade)

                    # Set stop loss, take profit, and trailing stop for the new trade
                    self.trade_manager.set_trade_exit_parameters(
                        trade_id=new_trade['trade_id'],
                        stop_loss_price=signal.get('stop_loss_price'),
                        take_profit_price=signal.get('take_profit_price'),
                        trailing_stop_pct=signal.get('trailing_stop_pct', 0.20)
                    )
                    logger.info(f"🚀 [TRADE] New trade {new_trade['trade_id']} opened with {new_trade['lot_size']} contracts.")

                    # Send alert for new trade
                    await self._send_alert(
                        "New Trade Opened",
                        f"Trade {new_trade['trade_id']} for {signal['symbol']} {signal['option_type']} opened with {new_trade['lot_size']} contracts at {signal['entry_price']:.2f}"
                    )
                else:
                    logger.warning(f"❌ [DECISION] Signal {signal['signal_id']} rejected: {risk_decision['reason']}")

                # Mark signal as processed
                self.pending_signals.task_done()

            except asyncio.CancelledError:
                logger.info("Signal processing task cancelled.")
                break
            except Exception as e:
                logger.error(f"❌ [SIGNAL_PROCESS_ERROR] An error occurred during signal processing: {e}", exc_info=True)
                await asyncio.sleep(1)  # Brief pause before retrying

    async def _enrich_signal_with_market_data(self, signal: Dict[str, Any]):
        """
        Enrich signal with real-time market data including current prices and Greeks.
        """
        try:
            symbol = signal.get('underlying', signal.get('symbol', 'NIFTY'))

            # Get current underlying price
            current_underlying_price = self.market_monitoring_agent.get_current_price(symbol, '1min')
            if current_underlying_price:
                signal['current_underlying_price'] = current_underlying_price
                logger.debug(f"📊 [ENRICH] Updated {symbol} current price: {current_underlying_price:.2f}")

            # Get market regime data
            regime_data = self.market_monitoring_agent.get_market_regime_data(symbol, '15min')
            if regime_data:
                signal['market_regime'] = regime_data
                logger.debug(f"📊 [ENRICH] Added market regime data for {symbol}")

            # Update Greeks with real-time calculation if we have current price
            if current_underlying_price and signal.get('strike_price'):
                updated_greeks = await self._calculate_real_time_greeks(
                    underlying_price=current_underlying_price,
                    strike_price=signal['strike_price'],
                    option_type=signal['option_type'],
                    time_to_expiry=signal.get('time_to_expiry_days', 30),
                    implied_volatility=signal.get('implied_volatility', 0.20)
                )
                if updated_greeks:
                    signal['greeks'] = updated_greeks
                    logger.debug(f"📊 [ENRICH] Updated Greeks for {signal['signal_id']}")

            # Update entry price if it's zero or missing
            if not signal.get('entry_price') or signal.get('entry_price') == 0:
                # Estimate option price based on intrinsic value and time value
                estimated_price = await self._estimate_option_price(signal)
                if estimated_price:
                    signal['entry_price'] = estimated_price
                    logger.debug(f"📊 [ENRICH] Estimated entry price for {signal['signal_id']}: {estimated_price:.2f}")

        except Exception as e:
            logger.error(f"❌ [ENRICH] Error enriching signal with market data: {e}", exc_info=True)

    async def _calculate_real_time_greeks(self, underlying_price: float, strike_price: float,
                                        option_type: str, time_to_expiry: int,
                                        implied_volatility: float) -> Optional[Dict[str, float]]:
        """
        Calculate real-time Greeks using Black-Scholes model.
        This replaces the static Greeks values.
        """
        try:
            import math
            from scipy.stats import norm

            # Convert inputs
            S = underlying_price  # Current stock price
            K = strike_price      # Strike price
            T = time_to_expiry / 365.0  # Time to expiry in years
            r = 0.06  # Risk-free rate (6%)
            sigma = implied_volatility  # Volatility

            if T <= 0:
                # Option has expired
                return {"delta": 0.0, "gamma": 0.0, "theta": 0.0, "vega": 0.0}

            # Calculate d1 and d2
            d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
            d2 = d1 - sigma * math.sqrt(T)

            # Calculate Greeks
            if option_type.lower() in ['call', 'ce']:
                # Call option Greeks
                delta = norm.cdf(d1)
                gamma = norm.pdf(d1) / (S * sigma * math.sqrt(T))
                theta = (-(S * norm.pdf(d1) * sigma) / (2 * math.sqrt(T)) -
                        r * K * math.exp(-r * T) * norm.cdf(d2)) / 365
                vega = S * norm.pdf(d1) * math.sqrt(T) / 100
            else:
                # Put option Greeks
                delta = -norm.cdf(-d1)
                gamma = norm.pdf(d1) / (S * sigma * math.sqrt(T))
                theta = (-(S * norm.pdf(d1) * sigma) / (2 * math.sqrt(T)) +
                        r * K * math.exp(-r * T) * norm.cdf(-d2)) / 365
                vega = S * norm.pdf(d1) * math.sqrt(T) / 100

            greeks = {
                "delta": round(delta, 4),
                "gamma": round(gamma, 6),
                "theta": round(theta, 4),
                "vega": round(vega, 4)
            }

            logger.debug(f"📊 [GREEKS] Calculated real-time Greeks: {greeks}")
            return greeks

        except ImportError:
            logger.warning("⚠️ [GREEKS] scipy not available, using simplified Greeks calculation")
            # Simplified Greeks calculation without scipy
            return await self._calculate_simplified_greeks(underlying_price, strike_price, option_type, time_to_expiry)
        except Exception as e:
            logger.error(f"❌ [GREEKS] Error calculating real-time Greeks: {e}")
            return None

    async def _calculate_simplified_greeks(self, underlying_price: float, strike_price: float,
                                         option_type: str, time_to_expiry: int) -> Dict[str, float]:
        """
        Simplified Greeks calculation without external dependencies.
        """
        try:
            # Simplified approximations
            moneyness = underlying_price / strike_price
            time_factor = max(0.1, time_to_expiry / 365.0)

            if option_type.lower() in ['call', 'ce']:
                if moneyness > 1.0:  # ITM call
                    delta = 0.6 + (moneyness - 1.0) * 0.3
                else:  # OTM call
                    delta = 0.4 * moneyness
            else:  # Put option
                if moneyness < 1.0:  # ITM put
                    delta = -0.6 - (1.0 - moneyness) * 0.3
                else:  # OTM put
                    delta = -0.4 * (2.0 - moneyness)

            # Simplified other Greeks
            gamma = 0.1 * time_factor
            theta = -0.05 * time_factor
            vega = 0.2 * time_factor

            return {
                "delta": round(max(-1.0, min(1.0, delta)), 4),
                "gamma": round(max(0.0, gamma), 6),
                "theta": round(min(0.0, theta), 4),
                "vega": round(max(0.0, vega), 4)
            }

        except Exception as e:
            logger.error(f"❌ [GREEKS_SIMPLE] Error in simplified Greeks calculation: {e}")
            # Return default values
            return {"delta": 0.5, "gamma": 0.1, "theta": -0.05, "vega": 0.2}

    async def _estimate_option_price(self, signal: Dict[str, Any]) -> Optional[float]:
        """
        Estimate option price based on intrinsic and time value.
        """
        try:
            underlying_price = signal.get('current_underlying_price')
            strike_price = signal.get('strike_price')
            option_type = signal.get('option_type', 'call')

            if not underlying_price or not strike_price:
                return None

            # Calculate intrinsic value
            if option_type.lower() in ['call', 'ce']:
                intrinsic_value = max(0, underlying_price - strike_price)
            else:
                intrinsic_value = max(0, strike_price - underlying_price)

            # Estimate time value (simplified)
            time_to_expiry = signal.get('time_to_expiry_days', 30)
            time_value = max(1.0, underlying_price * 0.01 * (time_to_expiry / 30))

            estimated_price = intrinsic_value + time_value
            logger.debug(f"📊 [PRICE_EST] Estimated option price: {estimated_price:.2f} (IV: {intrinsic_value:.2f}, TV: {time_value:.2f})")

            return estimated_price

        except Exception as e:
            logger.error(f"❌ [PRICE_EST] Error estimating option price: {e}")
            return None

    async def _estimate_current_option_price(self, trade_details: Dict[str, Any], current_underlying_price: float) -> Optional[float]:
        """
        Estimate current option price based on underlying price movement and Greeks.
        """
        try:
            entry_price = trade_details.get('entry_price', 0)
            entry_underlying_price = trade_details.get('entry_underlying_price', current_underlying_price)
            strike_price = trade_details.get('strike_price', 0)
            option_type = trade_details.get('option_type', 'call')
            greeks = trade_details.get('greeks', {})

            if not entry_price or not strike_price:
                return None

            # Calculate underlying price change
            underlying_change = current_underlying_price - entry_underlying_price

            # Estimate price change using Delta (simplified)
            delta = greeks.get('delta', 0.5)
            estimated_price_change = underlying_change * delta

            # Apply time decay (Theta) - assuming 1 day has passed (simplified)
            theta = greeks.get('theta', -0.05)
            time_decay = theta  # Theta is per day

            # Estimate current option price
            estimated_current_price = entry_price + estimated_price_change + time_decay

            # Ensure price doesn't go below intrinsic value
            if option_type.lower() in ['call', 'ce']:
                intrinsic_value = max(0, current_underlying_price - strike_price)
            else:
                intrinsic_value = max(0, strike_price - current_underlying_price)

            estimated_current_price = max(intrinsic_value, estimated_current_price)

            logger.debug(f"📊 [OPTION_PRICE_EST] Trade: Entry={entry_price:.2f}, Estimated={estimated_current_price:.2f}, Underlying Δ={underlying_change:.2f}")

            return estimated_current_price

        except Exception as e:
            logger.error(f"❌ [OPTION_PRICE_EST] Error estimating current option price: {e}")
            return None

    async def _update_trade_greeks(self, trade_details: Dict[str, Any], current_underlying_price: float):
        """
        Update trade Greeks with current market data.
        """
        try:
            strike_price = trade_details.get('strike_price', 0)
            option_type = trade_details.get('option_type', 'call')
            time_to_expiry = trade_details.get('time_to_expiry_days', 30)
            implied_volatility = trade_details.get('implied_volatility', 0.20)

            if strike_price and current_underlying_price:
                # Calculate updated Greeks
                updated_greeks = await self._calculate_real_time_greeks(
                    underlying_price=current_underlying_price,
                    strike_price=strike_price,
                    option_type=option_type,
                    time_to_expiry=time_to_expiry,
                    implied_volatility=implied_volatility
                )

                if updated_greeks:
                    trade_details['greeks'] = updated_greeks
                    logger.debug(f"📊 [GREEKS_UPDATE] Updated Greeks for trade: {updated_greeks}")

        except Exception as e:
            logger.error(f"❌ [GREEKS_UPDATE] Error updating trade Greeks: {e}")

    async def _reset_daily_metrics(self):
        """
        Resets daily metrics at the start of a new trading day.
        Includes enhanced exception handling and logging.
        """
        try:
            now = datetime.now()
            # Check if it's a new day or if the reset date hasn't been set yet
            if not hasattr(self, '_last_daily_reset_date') or self._last_daily_reset_date != now.date():
                logger.info("☀️ [DAILY RESET] Resetting daily risk metrics and trade manager stats.")
                
                # Reset agent-specific daily metrics
                self.daily_high_capital = self.current_capital
                self.daily_drawdown_loss = 0.0
                self.daily_drawdown_pct = 0.0
                self.daily_capital_used = 0.0
                self.daily_trades_count = 0
                
                # Reset TradeManager specific metrics
                self.trade_manager.win_loss_streak.clear()
                self.trade_manager.consecutive_sl_hits = 0
                self.trade_manager.last_trade_time.clear()

                # Reset trading pause status
                self.trading_paused = False
                self.pause_cool_off_until = None
                
                # Update the last reset date
                self._last_daily_reset_date = now.date()
                logger.info("✅ [DAILY RESET] Daily metrics and trade manager stats reset successfully.")
            else:
                logger.debug("☀️ [DAILY RESET] No reset needed, today's metrics already processed.")
        except Exception as e:
            logger.error(f"❌ [DAILY RESET ERROR] Failed to reset daily metrics: {e}", exc_info=True)
            # Depending on the severity, you might want to set a flag or take other actions

# Example usage
async def main():
    agent = OptionsRiskManagementAgent()
    try:
        await agent.initialize()
        await agent.start()
        # ... simulation logic ...
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
