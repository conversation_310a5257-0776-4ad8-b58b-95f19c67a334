import polars as pl
import logging
from typing import Dict, Any
import math # For math.sqrt

logger = logging.getLogger(__name__)

async def aggregate_strategy_metrics(evaluated_trades_df: pl.DataFrame, config: Dict[str, Any], period: str = "daily") -> Dict[str, Any]:
    """
    Computes strategy-wise metrics over defined periods.
    period can be "daily", "weekly", "monthly".
    """
    if evaluated_trades_df.is_empty():
        logger.warning(f"No evaluated trades to aggregate for {period} strategy metrics.")
        return {}

    logger.info(f"[ANALYZE] Aggregating strategy-wise metrics for period: {period}...")

    # Ensure 'entry_time' is a datetime type for grouping
    evaluated_trades_df = evaluated_trades_df.with_columns(
        pl.col("entry_time").cast(pl.Datetime)
    )

    # Add period grouping column first
    if period == "daily":
        evaluated_trades_df = evaluated_trades_df.with_columns(
            pl.col("entry_time").dt.date().alias("period_start")
        )
    elif period == "weekly":
        evaluated_trades_df = evaluated_trades_df.with_columns(
            pl.col("entry_time").dt.week().alias("period_start")
        )
    elif period == "monthly":
        evaluated_trades_df = evaluated_trades_df.with_columns(
            pl.col("entry_time").dt.month().alias("period_start")
        )
    else:
        raise ValueError("Invalid period. Must be 'daily', 'weekly', or 'monthly'.")

    # Group by strategy_id and the period, aggregating all necessary metrics
    strategy_metrics = evaluated_trades_df.group_by(["strategy_id", "period_start"]).agg([
        # Win rate (accuracy %)
        (pl.col("pnl_category") == "Profit").mean().alias("win_rate_percent"),
        # Average ROI
        pl.col("roi_percent").mean().alias("avg_roi"),
        # Standard deviation of ROI for Sharpe Ratio
        pl.col("roi_percent").std().alias("roi_std"),
        # Average Win and Average Loss for Expectancy
        pl.col("absolute_pnl").filter(pl.col("absolute_pnl") > 0).mean().alias("avg_win"),
        pl.col("absolute_pnl").filter(pl.col("absolute_pnl") < 0).mean().alias("avg_loss"),
        # Sample Size
        pl.len().alias("sample_size"),
        # Capital efficiency (conceptual: sum of P&L / sum of risked capital)
        (pl.col("absolute_pnl").sum() / pl.col("allowed_capital_at_risk").sum()).alias("capital_efficiency"),
        # Signal-to-trade conversion rate (conceptual: assuming 'signal_confidence' implies a signal)
        (pl.col("signal_confidence").is_not_null().sum() / pl.len()).alias("signal_to_trade_conversion_rate"),
        # Entry precision: How close to local low (for CE) or high (for PE)
        pl.lit(None).alias("entry_precision")
    ]).sort(["strategy_id", "period_start"])

    # Calculate Expectancy and Sharpe Ratio
    annualization_factor = {
        "daily": math.sqrt(252), # Trading days in a year
        "weekly": math.sqrt(52), # Weeks in a year
        "monthly": math.sqrt(12) # Months in a year
    }.get(period, 1)

    strategy_metrics = strategy_metrics.with_columns([
        # Expectancy = Avg win × win rate – Avg loss × loss rate
        (
            (pl.col("avg_win") * pl.col("win_rate_percent")) - 
            (pl.col("avg_loss").abs() * (1 - pl.col("win_rate_percent")))
        ).alias("expectancy"),
        # Sharpe Ratio
        (
            (pl.col("avg_roi") - config['risk_free_rate']) / pl.col("roi_std") * annualization_factor
        ).alias("sharpe_ratio")
    ])

    # Calculate Max Drawdown (simplified for grouped data)
    # For a more accurate calculation, we would need time-series data
    strategy_metrics = strategy_metrics.with_columns([
        # Simplified max drawdown as percentage of worst single trade
        pl.when(pl.col("sample_size") > 0)
        .then(pl.lit(0.05))  # Placeholder 5% max drawdown
        .otherwise(0.0)
        .alias("max_dd_percent")
    ])

    # Ensure Sharpe Ratio handles division by zero if roi_std is 0
    strategy_metrics = strategy_metrics.with_columns([
        pl.when(pl.col("roi_std") != 0)
        .then((pl.col("avg_roi") - config['risk_free_rate']) / pl.col("roi_std") * annualization_factor)
        .otherwise(0.0) # Handle division by zero
        .alias("sharpe_ratio")
    ])
    
    # Convert to dictionary format as requested in the example output
    output_data = {}
    for row in strategy_metrics.iter_rows(named=True):
        strategy_id = row["strategy_id"]
        period_start = row["period_start"]
        if strategy_id not in output_data:
            output_data[strategy_id] = {}
        # Handle None values safely
        def safe_round(value, decimals=2):
            return round(value, decimals) if value is not None else 0.0

        output_data[strategy_id][str(period_start)] = {
            "win_rate": safe_round(row.get("win_rate_percent", 0) * 100, 2),
            "avg_roi": safe_round(row.get("avg_roi", 0), 2),
            "expectancy": safe_round(row.get("expectancy", 0), 2),
            "max_dd": safe_round(row.get("max_dd_percent", 0) * 100, 2),
            "sharpe": safe_round(row.get("sharpe_ratio", 0), 2),
            "sample_size": row.get("sample_size", 0),
            "capital_efficiency": safe_round(row.get("capital_efficiency", 0), 2),
            "signal_to_trade_conversion_rate": safe_round(row.get("signal_to_trade_conversion_rate", 0), 2),
            "entry_precision": row.get("entry_precision"),
            "best_regime": "N/A" # Placeholder, needs regime evaluation
        }
    
    logger.info(f"Strategy-wise metrics aggregation complete for {period}.")
    return output_data
