import logging
from datetime import timed<PERSON><PERSON>
from pathlib import Path
from typing import Dict, Any, Optional
import polars as pl
import math

# Lazy load numpy, scipy
import importlib.util

def lazy_import(name):
    spec = importlib.util.find_spec(name)
    if spec is None:
        return None
    loader = importlib.util.LazyLoader(spec.loader)
    spec.loader = loader
    module = importlib.util.module_from_spec(spec)
    return module

np = lazy_import("numpy")
stats = lazy_import("scipy.stats")

from agents.performance_analysis.schemas import PerformanceMetrics
from agents.performance_analysis.data_loader import load_historical_underlying_data, load_greeks_data

logger = logging.getLogger(__name__)

async def calculate_greeks_pnl_attribution(trades_df: pl.DataFrame, greeks_df: pl.DataFrame) -> pl.DataFrame:
    """
    Calculate P&L attribution from Options Greeks
    Breaks down P&L into Delta, Gamma, Theta, Vega, and Rho components
    """
    if trades_df.is_empty() or greeks_df.is_empty():
        logger.warning("No data available for Greeks P&L attribution.")
        return trades_df

    logger.info("📊 Calculating Greeks P&L attribution...")

    trade_times = trades_df.select(["trade_id", "entry_time", "exit_time"])

    filtered_greeks = greeks_df.join(trade_times, on="trade_id", how="inner").filter(
        (pl.col("timestamp") >= pl.col("entry_time")) &
        (pl.col("timestamp") <= pl.col("exit_time"))
    )

    greeks_agg = filtered_greeks.group_by("trade_id").agg([
        pl.col("delta").first().alias("entry_delta"),
        pl.col("delta").last().alias("exit_delta"),
        pl.col("gamma").first().alias("entry_gamma"),
        pl.col("gamma").last().alias("exit_gamma"),
        pl.col("theta").first().alias("entry_theta"),
        pl.col("theta").last().alias("exit_theta"),
        pl.col("vega").first().alias("entry_vega"),
        pl.col("vega").last().alias("exit_vega"),
        pl.col("rho").first().alias("entry_rho"),
        pl.col("rho").last().alias("exit_rho"),
        pl.col("implied_volatility").first().alias("entry_iv"),
        pl.col("implied_volatility").last().alias("exit_iv"),
        pl.col("time_to_expiry").first().alias("entry_tte"),
        pl.col("time_to_expiry").last().alias("exit_tte")
    ])

    trades_with_greeks = trades_df.join(greeks_agg, on="trade_id", how="left")

    trades_with_greeks = trades_with_greeks.with_columns([
        (
            (pl.col("underlying_exit_price") - pl.col("underlying_entry_price")) *
            ((pl.col("entry_delta") + pl.col("exit_delta")) / 2) *
            pl.col("quantity")
        ).alias("delta_pnl"),
        (
            0.5 * ((pl.col("entry_gamma") + pl.col("exit_gamma")) / 2) *
            (pl.col("underlying_exit_price") - pl.col("underlying_entry_price")).pow(2) *
            pl.col("quantity")
        ).alias("gamma_pnl"),
        (
            (pl.col("entry_tte") - pl.col("exit_tte")) *
            ((pl.col("entry_theta") + pl.col("exit_theta")) / 2) *
            pl.col("quantity")
        ).alias("theta_pnl"),
        (
            (pl.col("exit_iv") - pl.col("entry_iv")) *
            ((pl.col("entry_vega") + pl.col("exit_vega")) / 2) *
            pl.col("quantity")
        ).alias("vega_pnl"),
        (
            0.01 * ((pl.col("entry_rho") + pl.col("exit_rho")) / 2) *
            pl.col("quantity")
        ).alias("rho_pnl")
    ])

    trades_with_greeks = trades_with_greeks.with_columns([
        (
            pl.col("delta_pnl") + pl.col("gamma_pnl") +
            pl.col("theta_pnl") + pl.col("vega_pnl") + pl.col("rho_pnl")
        ).alias("total_greeks_pnl"),
        (
            pl.col("absolute_pnl") -
            (pl.col("delta_pnl") + pl.col("gamma_pnl") +
             pl.col("theta_pnl") + pl.col("vega_pnl") + pl.col("rho_pnl"))
        ).alias("residual_pnl")
    ])

    logger.info("✅ Greeks P&L attribution calculation complete.")
    return trades_with_greeks

async def calculate_volatility_pnl_analysis(trades_df: pl.DataFrame) -> pl.DataFrame:
    """
    Volatility P&L Analysis
    Analyzes P&L attribution from volatility changes
    """
    if trades_df.is_empty():
        logger.warning("No data available for volatility P&L analysis.")
        return trades_df

    logger.info("📈 Calculating volatility P&L analysis...")

    trades_df = trades_df.with_columns([
        pl.when(pl.col("entry_iv") < 0.15)
        .then(pl.lit("Low IV"))
        .when(pl.col("entry_iv") < 0.25)
        .then(pl.lit("Medium IV"))
        .otherwise(pl.lit("High IV")).alias("iv_regime"),
        (pl.col("exit_iv") - pl.col("entry_iv")).alias("iv_change"),
        pl.when(pl.col("vega_pnl").abs() > 0)
        .then(pl.col("absolute_pnl") / pl.col("vega_pnl").abs())
        .otherwise(0.0).alias("vol_pnl_efficiency")
    ])

    logger.info("✅ Volatility P&L analysis complete.")
    return trades_df

async def evaluate_trade_level_performance(trades_df: pl.DataFrame, historical_data_path: Optional[Path] = None, greeks_data_path: Optional[Path] = None) -> pl.DataFrame:
    """
    Analyzes each completed trade and adds performance metrics.
    Adds columns: pnl_category, was_signal_accurate, slippage_reason, execution_score, entry_precision
    """
    if trades_df.is_empty():
        logger.warning("No trades to evaluate for trade-level performance.")
        return trades_df

    logger.info("[ANALYZE] Evaluating trade-level performance...")

    trades_df = trades_df.with_columns([
        ((pl.col("exit_price") - pl.col("entry_price")) / pl.col("entry_price") * 100).alias("roi_percent"),
        ((pl.col("exit_price") - pl.col("entry_price")) * pl.col("quantity")).alias("absolute_pnl"),
        ((pl.col("exit_time") - pl.col("entry_time")).dt.total_minutes()).alias("holding_time_minutes"),
        (pl.col("entry_price") - pl.col("expected_entry_price")).alias("entry_slippage"),
        (pl.col("exit_price") - pl.col("expected_exit_price")).alias("exit_slippage"),
    ])

    trades_df = trades_df.with_columns(
        pl.when(pl.col("is_target_hit"))
        .then(pl.lit("Target hit"))
        .when(pl.col("is_sl_hit"))
        .then(pl.lit("SL hit"))
        .when(pl.col("is_manual_exit"))
        .then(pl.lit("Manual exit"))
        .otherwise(pl.lit("Unknown exit")).alias("exit_reason")
    )

    trades_df = trades_df.with_columns(
        pl.when(pl.col("absolute_pnl") > 0)
        .then(pl.lit("Profit"))
        .when(pl.col("absolute_pnl") < 0)
        .then(pl.lit("Loss"))
        .otherwise(pl.lit("Break-even")).alias("pnl_category")
    )

    trades_df = trades_df.with_columns(
        (pl.col("absolute_pnl") > 0).alias("was_signal_accurate")
    )

    trades_df = trades_df.with_columns(
        pl.when((pl.col("entry_slippage").abs() > 0.1) | (pl.col("exit_slippage").abs() > 0.1))
        .then(pl.lit("Significant Slippage"))
        .otherwise(pl.lit("Minimal Slippage")).alias("slippage_reason")
    )

    trades_df = trades_df.with_columns(
        (pl.col("signal_confidence") * pl.col("roi_percent").sign()).alias("confidence_outcome_correlation")
    )

    trades_df = trades_df.with_columns(
        (
            (1 - (pl.col("entry_slippage").abs() + pl.col("exit_slippage").abs())) *
            (1 / (pl.col("holding_time_minutes") + 1))
        ).alias("execution_score")
    )
    
    if historical_data_path:
        historical_df = await load_historical_underlying_data(historical_data_path)
        if historical_df is not None and not historical_df.is_empty():
            historical_df = historical_df.with_columns(pl.col("timestamp").cast(pl.Datetime)).sort("timestamp")

            def calculate_entry_precision(row: Dict[str, Any]) -> Optional[float]:
                entry_time = row["entry_time"]
                underlying_entry_price = row["underlying_entry_price"]
                option_type = row["option_type"]

                window_start = entry_time - timedelta(minutes=5)
                window_end = entry_time

                window_data = historical_df.filter(
                    (pl.col("timestamp") >= window_start) & (pl.col("timestamp") < window_end)
                )

                if window_data.is_empty():
                    return None

                if option_type == "CALL":
                    local_low = window_data.select(pl.col("close").min()).item()
                    if local_low is None: return None
                    price_range = window_data.select(pl.col("close").max()).item() - local_low
                    if price_range == 0: return 1.0
                    return 1 - ((underlying_entry_price - local_low) / price_range)
                elif option_type == "PUT":
                    local_high = window_data.select(pl.col("close").max()).item()
                    if local_high is None: return None
                    price_range = local_high - window_data.select(pl.col("close").min()).item()
                    if price_range == 0: return 1.0
                    return 1 - ((local_high - underlying_entry_price) / price_range)
                return None

            trades_df = trades_df.with_columns(
                pl.struct(
                    ["entry_time", "underlying_entry_price", "option_type"]
                ).apply(calculate_entry_precision).alias("entry_precision")
            )
        else:
            logger.warning("Historical underlying data not loaded or is empty. Entry precision will be null.")
            trades_df = trades_df.with_columns(pl.lit(None, dtype=pl.Float64).alias("entry_precision"))
    else:
        logger.warning("No historical data path provided. Entry precision will be null.")
        trades_df = trades_df.with_columns(pl.lit(None, dtype=pl.Float64).alias("entry_precision"))
    
    greeks_df = await load_greeks_data(greeks_data_path)
    if greeks_df is not None and not greeks_df.is_empty():
        trades_df = await calculate_greeks_pnl_attribution(trades_df, greeks_df)
        trades_df = await calculate_volatility_pnl_analysis(trades_df)

    logger.info("✅ Trade-level performance evaluation complete. Added new columns.")
    return trades_df

async def calculate_advanced_risk_metrics(returns_series: pl.Series, config: Dict[str, Any]) -> PerformanceMetrics:
    """
    Calculate advanced risk-adjusted performance metrics
    """
    if returns_series.is_empty():
        return PerformanceMetrics(0, 0, 0, 0, 0, 0, 0, 0)

    logger.info("📊 Calculating advanced risk metrics...")

    if np is None:
        logger.error("Numpy not loaded. Cannot calculate advanced risk metrics.")
        return PerformanceMetrics(0, 0, 0, 0, 0, 0, 0, 0)

    returns_np = returns_series.to_numpy()
    returns_np = returns_np[~np.isnan(returns_np)]

    if len(returns_np) == 0:
        return PerformanceMetrics(0, 0, 0, 0, 0, 0, 0, 0)

    mean_return = np.mean(returns_np)
    std_return = np.std(returns_np)

    negative_returns = returns_np[returns_np < 0]
    downside_deviation = np.std(negative_returns) if len(negative_returns) > 0 else 0

    risk_free_rate = config['risk_free_rate'] / 252
    sharpe_ratio = (mean_return - risk_free_rate) / std_return if std_return > 0 else 0
    sortino_ratio = (mean_return - risk_free_rate) / downside_deviation if downside_deviation > 0 else 0

    cumulative_returns = np.cumprod(1 + returns_np)
    running_max = np.maximum.accumulate(cumulative_returns)
    drawdown = (cumulative_returns - running_max) / running_max
    max_drawdown = np.min(drawdown)

    calmar_ratio = mean_return / abs(max_drawdown) if max_drawdown != 0 else 0

    var_95 = np.percentile(returns_np, 5)
    cvar_95 = np.mean(returns_np[returns_np <= var_95]) if len(returns_np[returns_np <= var_95]) > 0 else 0

    win_rate = len(returns_np[returns_np > 0]) / len(returns_np)

    gross_profit = np.sum(returns_np[returns_np > 0])
    gross_loss = abs(np.sum(returns_np[returns_np < 0]))
    profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

    return PerformanceMetrics(
        sharpe_ratio=sharpe_ratio,
        sortino_ratio=sortino_ratio,
        calmar_ratio=calmar_ratio,
        max_drawdown=max_drawdown,
        var_95=var_95,
        cvar_95=cvar_95,
        win_rate=win_rate,
        profit_factor=profit_factor
    )

async def monte_carlo_simulation(returns_series: pl.Series, num_simulations: int = 10000) -> Dict[str, float]:
    """
    Monte Carlo simulation for risk assessment
    """
    if returns_series.is_empty():
        return {"mean_return": 0, "std_return": 0, "var_95": 0, "var_99": 0}

    logger.info(f"🎲 Running Monte Carlo simulation with {num_simulations} iterations...")

    if np is None or stats is None:
        logger.error("Numpy or Scipy not loaded. Cannot run Monte Carlo simulation.")
        return {"mean_return": 0, "std_return": 0, "var_95": 0, "var_99": 0}

    returns_np = returns_series.to_numpy()
    returns_np = returns_np[~np.isnan(returns_np)]

    if len(returns_np) == 0:
        return {"mean_return": 0, "std_return": 0, "var_95": 0, "var_99": 0}

    mu, sigma = stats.norm.fit(returns_np)
    simulated_returns = np.random.normal(mu, sigma, num_simulations)

    mc_results = {
        "mean_return": np.mean(simulated_returns),
        "std_return": np.std(simulated_returns),
        "var_95": np.percentile(simulated_returns, 5),
        "var_99": np.percentile(simulated_returns, 1),
        "expected_shortfall_95": np.mean(simulated_returns[simulated_returns <= np.percentile(simulated_returns, 5)]),
        "probability_of_loss": len(simulated_returns[simulated_returns < 0]) / num_simulations
    }

    logger.info("✅ Monte Carlo simulation complete.")
    return mc_results

async def stress_testing(trades_df: pl.DataFrame, config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Stress testing under various market scenarios
    """
    if trades_df.is_empty():
        return {}

    logger.info("⚡ Running stress testing scenarios...")

    stress_results = {}
    scenarios = config['stress_test_scenarios']

    for scenario_name, shock_value in scenarios.items():
        logger.info(f"Testing scenario: {scenario_name} with shock: {shock_value}")

        stressed_trades = trades_df.clone() # Create a copy to avoid modifying original DF

        if scenario_name == "market_crash":
            stressed_trades = stressed_trades.with_columns([
                (pl.col("underlying_exit_price") * (1 + shock_value)).alias("stressed_underlying_price"),
                (pl.col("delta_pnl") * (1 + shock_value)).alias("stressed_delta_pnl"),
                (pl.col("gamma_pnl") * (1 + shock_value * 2)).alias("stressed_gamma_pnl")
            ])
            stressed_trades = stressed_trades.with_columns([
                (pl.col("stressed_delta_pnl") + pl.col("stressed_gamma_pnl") +
                 pl.col("theta_pnl") + pl.col("vega_pnl") + pl.col("rho_pnl")
                ).alias("stressed_total_pnl")
            ])

        elif scenario_name == "volatility_spike":
            stressed_trades = stressed_trades.with_columns([
                (pl.col("vega_pnl") * shock_value).alias("stressed_vega_pnl"),
                (pl.col("absolute_pnl") + pl.col("vega_pnl") * (shock_value - 1)).alias("stressed_total_pnl")
            ])

        elif scenario_name == "interest_rate_shock":
            stressed_trades = stressed_trades.with_columns([
                (pl.col("rho_pnl") + pl.col("quantity") * shock_value).alias("stressed_rho_pnl"),
                (pl.col("absolute_pnl") + pl.col("quantity") * shock_value).alias("stressed_total_pnl")
            ])

        if "stressed_total_pnl" in stressed_trades.columns:
            stressed_returns = stressed_trades.select(pl.col("stressed_total_pnl")).to_series()
            stress_metrics = await calculate_advanced_risk_metrics(stressed_returns, config)
            stress_results[scenario_name] = {
                "total_pnl": stressed_returns.sum(),
                "max_drawdown": stress_metrics.max_drawdown,
                "var_95": stress_metrics.var_95,
                "win_rate": stress_metrics.win_rate
            }

    logger.info("✅ Stress testing complete.")
    return stress_results
