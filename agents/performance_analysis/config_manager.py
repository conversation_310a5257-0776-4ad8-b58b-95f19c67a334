#!/usr/bin/env python3
"""
Configuration Management Module for Options Performance Analysis Agent

This module handles:
- YAML configuration loading and validation
- Environment-specific configuration management
- Configuration schema validation
- Default configuration values
"""

import logging
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field, validator
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class AlertThresholds:
    """Alert threshold configuration"""
    max_drawdown: float = 0.15  # 15%
    daily_loss: float = 0.05    # 5%
    win_rate_drop: float = 0.10 # 10%
    sharpe_drop: float = 0.5    # 0.5 points

@dataclass
class NotificationSettings:
    """Notification configuration"""
    enable_windows_notifications: bool = True
    enable_email_alerts: bool = False
    alert_thresholds: AlertThresholds = None
    
    def __post_init__(self):
        if self.alert_thresholds is None:
            self.alert_thresholds = AlertThresholds()

@dataclass
class DataPaths:
    """Data file paths configuration"""
    trades_data: str = "data/trades/completed_trades.parquet"
    historical_data: str = "data/historical/underlying_data.parquet"
    greeks_data: str = "data/greeks/options_greeks.parquet"
    export_path: str = "exports"

@dataclass
class AnalysisSettings:
    """Analysis configuration"""
    analysis_interval: int = 300  # seconds
    risk_free_rate: float = 0.06  # 6% annual
    min_trades_for_analysis: int = 10
    monte_carlo_simulations: int = 10000
    confidence_levels: list = None
    
    def __post_init__(self):
        if self.confidence_levels is None:
            self.confidence_levels = [0.95, 0.99]

@dataclass
class PerformanceConfig:
    """Performance optimization configuration"""
    max_workers: int = 5
    enable_lazy_loading: bool = True
    cache_size: int = 1000
    batch_size: int = 1000

class OptionsPerformanceConfig:
    """Main configuration class for Options Performance Analysis Agent"""
    
    def __init__(self, config_path: Optional[Path] = None):
        self.config_path = config_path or Path("config/options_performance_analysis_config.yaml")
        self.data_paths = DataPaths()
        self.analysis_settings = AnalysisSettings()
        self.notification_settings = NotificationSettings()
        self.performance_config = PerformanceConfig()
        self._config_data: Dict[str, Any] = {}
        
    async def load_config(self) -> bool:
        """Load configuration from YAML file with validation"""
        try:
            if not self.config_path.exists():
                logger.warning(f"Configuration file not found at {self.config_path}. Creating default config.")
                await self._create_default_config()
                return True
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config_data = yaml.safe_load(f)
            
            # Apply loaded configuration to dataclasses
            self._apply_config()
            
            logger.info(f"Configuration loaded successfully from {self.config_path}")
            return True
            
        except yaml.YAMLError as e:
            logger.error(f"Error loading YAML configuration from {self.config_path}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error loading configuration: {e}")
            return False
    
    def _apply_config(self):
        """Apply loaded configuration to dataclass instances"""
        # Apply data paths
        if 'data_paths' in self._config_data:
            data_config = self._config_data['data_paths']
            self.data_paths.trades_data = data_config.get('trades_data', self.data_paths.trades_data)
            self.data_paths.historical_data = data_config.get('historical_data', self.data_paths.historical_data)
            self.data_paths.greeks_data = data_config.get('greeks_data', self.data_paths.greeks_data)
            self.data_paths.export_path = data_config.get('export_path', self.data_paths.export_path)
        
        # Apply analysis settings
        if 'analysis_settings' in self._config_data:
            analysis_config = self._config_data['analysis_settings']
            self.analysis_settings.analysis_interval = analysis_config.get('analysis_interval', self.analysis_settings.analysis_interval)
            self.analysis_settings.risk_free_rate = analysis_config.get('risk_free_rate', self.analysis_settings.risk_free_rate)
            self.analysis_settings.min_trades_for_analysis = analysis_config.get('min_trades_for_analysis', self.analysis_settings.min_trades_for_analysis)
            self.analysis_settings.monte_carlo_simulations = analysis_config.get('monte_carlo_simulations', self.analysis_settings.monte_carlo_simulations)
            self.analysis_settings.confidence_levels = analysis_config.get('confidence_levels', self.analysis_settings.confidence_levels)
        
        # Apply notification settings
        if 'notification_settings' in self._config_data:
            notif_config = self._config_data['notification_settings']
            self.notification_settings.enable_windows_notifications = notif_config.get('enable_windows_notifications', self.notification_settings.enable_windows_notifications)
            self.notification_settings.enable_email_alerts = notif_config.get('enable_email_alerts', self.notification_settings.enable_email_alerts)
            
            # Apply alert thresholds
            if 'alert_thresholds' in notif_config:
                threshold_config = notif_config['alert_thresholds']
                self.notification_settings.alert_thresholds.max_drawdown = threshold_config.get('max_drawdown', self.notification_settings.alert_thresholds.max_drawdown)
                self.notification_settings.alert_thresholds.daily_loss = threshold_config.get('daily_loss', self.notification_settings.alert_thresholds.daily_loss)
                self.notification_settings.alert_thresholds.win_rate_drop = threshold_config.get('win_rate_drop', self.notification_settings.alert_thresholds.win_rate_drop)
                self.notification_settings.alert_thresholds.sharpe_drop = threshold_config.get('sharpe_drop', self.notification_settings.alert_thresholds.sharpe_drop)
        
        # Apply performance config
        if 'performance' in self._config_data:
            perf_config = self._config_data['performance']
            self.performance_config.max_workers = perf_config.get('max_workers', self.performance_config.max_workers)
            self.performance_config.enable_lazy_loading = perf_config.get('enable_lazy_loading', self.performance_config.enable_lazy_loading)
            self.performance_config.cache_size = perf_config.get('cache_size', self.performance_config.cache_size)
            self.performance_config.batch_size = perf_config.get('batch_size', self.performance_config.batch_size)

    async def _create_default_config(self):
        """Create default configuration file"""
        default_config = {
            'data_paths': {
                'trades_data': self.data_paths.trades_data,
                'historical_data': self.data_paths.historical_data,
                'greeks_data': self.data_paths.greeks_data,
                'export_path': self.data_paths.export_path
            },
            'analysis_settings': {
                'analysis_interval': self.analysis_settings.analysis_interval,
                'risk_free_rate': self.analysis_settings.risk_free_rate,
                'min_trades_for_analysis': self.analysis_settings.min_trades_for_analysis,
                'monte_carlo_simulations': self.analysis_settings.monte_carlo_simulations,
                'confidence_levels': self.analysis_settings.confidence_levels
            },
            'notification_settings': {
                'enable_windows_notifications': self.notification_settings.enable_windows_notifications,
                'enable_email_alerts': self.notification_settings.enable_email_alerts,
                'alert_thresholds': {
                    'max_drawdown': self.notification_settings.alert_thresholds.max_drawdown,
                    'daily_loss': self.notification_settings.alert_thresholds.daily_loss,
                    'win_rate_drop': self.notification_settings.alert_thresholds.win_rate_drop,
                    'sharpe_drop': self.notification_settings.alert_thresholds.sharpe_drop
                }
            },
            'performance': {
                'max_workers': self.performance_config.max_workers,
                'enable_lazy_loading': self.performance_config.enable_lazy_loading,
                'cache_size': self.performance_config.cache_size,
                'batch_size': self.performance_config.batch_size
            }
        }

        # Ensure config directory exists
        self.config_path.parent.mkdir(parents=True, exist_ok=True)

        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, indent=2)

        logger.info(f"Default configuration created at {self.config_path}")

    def get_config_value(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key with dot notation support"""
        keys = key.split('.')
        value = self._config_data

        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default

    def validate_config(self) -> bool:
        """Validate configuration values"""
        try:
            # Validate risk-free rate
            if not 0 <= self.analysis_settings.risk_free_rate <= 1:
                logger.error("Risk-free rate must be between 0 and 1")
                return False

            # Validate alert thresholds
            if not 0 < self.notification_settings.alert_thresholds.max_drawdown <= 1:
                logger.error("Max drawdown threshold must be between 0 and 1")
                return False

            # Validate paths
            for path_attr in ['trades_data', 'historical_data', 'greeks_data']:
                path_value = getattr(self.data_paths, path_attr)
                if not isinstance(path_value, str) or not path_value.strip():
                    logger.error(f"Invalid path for {path_attr}: {path_value}")
                    return False

            logger.info("Configuration validation passed")
            return True

        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'data_paths': {
                'trades_data': self.data_paths.trades_data,
                'historical_data': self.data_paths.historical_data,
                'greeks_data': self.data_paths.greeks_data,
                'export_path': self.data_paths.export_path
            },
            'analysis_settings': {
                'analysis_interval': self.analysis_settings.analysis_interval,
                'risk_free_rate': self.analysis_settings.risk_free_rate,
                'min_trades_for_analysis': self.analysis_settings.min_trades_for_analysis,
                'monte_carlo_simulations': self.analysis_settings.monte_carlo_simulations,
                'confidence_levels': self.analysis_settings.confidence_levels
            },
            'notification_settings': {
                'enable_windows_notifications': self.notification_settings.enable_windows_notifications,
                'enable_email_alerts': self.notification_settings.enable_email_alerts,
                'alert_thresholds': {
                    'max_drawdown': self.notification_settings.alert_thresholds.max_drawdown,
                    'daily_loss': self.notification_settings.alert_thresholds.daily_loss,
                    'win_rate_drop': self.notification_settings.alert_thresholds.win_rate_drop,
                    'sharpe_drop': self.notification_settings.alert_thresholds.sharpe_drop
                }
            },
            'performance': {
                'max_workers': self.performance_config.max_workers,
                'enable_lazy_loading': self.performance_config.enable_lazy_loading,
                'cache_size': self.performance_config.cache_size,
                'batch_size': self.performance_config.batch_size
            }
        }
