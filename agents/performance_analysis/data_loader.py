import asyncio
import logging
from pathlib import Path
from typing import Optional
import polars as pl

from agents.performance_analysis.schemas import (
    TradeDataSchema, HistoricalDataSchema, GreeksDataSchema,
    pydantic_type_to_polars_type
)

logger = logging.getLogger(__name__)

async def read_parquet_async(path: Path) -> pl.DataFrame:
    """Asynchronously read a Parquet file using asyncio.to_thread."""
    return await asyncio.to_thread(pl.read_parquet, path)

async def load_trade_data(trade_data_path: Path) -> Optional[pl.DataFrame]:
    """Load completed trade data from a Parquet file with schema enforcement."""
    if not trade_data_path.exists():
        logger.warning(f"Trade data file not found at {trade_data_path}. Returning empty DataFrame.")
        empty_df_schema = {field.name: pydantic_type_to_polars_type(field.outer_type_) for field in TradeDataSchema.__fields__.values()}
        return pl.DataFrame({}, schema=empty_df_schema)
    try:
        df = await read_parquet_async(trade_data_path)
        logger.info(f"Successfully loaded {len(df)} trades from {trade_data_path}")
        return df
    except Exception as e:
        logger.error(f"[ERROR] Failed to load trade data: {e}")
        return None

async def load_historical_underlying_data(path: Path) -> Optional[pl.DataFrame]:
    """Load historical underlying price data from a Parquet file with schema enforcement."""
    if not path.exists():
        logger.warning(f"Historical underlying data file not found at {path}. Returning empty DataFrame.")
        empty_df_schema = {field.name: pydantic_type_to_polars_type(field.outer_type_) for field in HistoricalDataSchema.__fields__.values()}
        return pl.DataFrame({}, schema=empty_df_schema)
    try:
        df = await read_parquet_async(path)
        logger.info(f"Successfully loaded {len(df)} records from {path}")
        return df
    except Exception as e:
        logger.error(f"[ERROR] Failed to load historical underlying data from {path}: {e}")
        return None

async def load_greeks_data(greeks_data_path: Path) -> Optional[pl.DataFrame]:
    """Load options Greeks data from a Parquet file with schema enforcement."""
    if not greeks_data_path.exists():
        logger.warning(f"Greeks data file not found at {greeks_data_path}. Returning empty DataFrame.")
        empty_df_schema = {field.name: pydantic_type_to_polars_type(field.outer_type_) for field in GreeksDataSchema.__fields__.values()}
        return pl.DataFrame({}, schema=empty_df_schema)
    try:
        df = await read_parquet_async(greeks_data_path)
        logger.info(f"Successfully loaded {len(df)} Greeks records from {greeks_data_path}")
        return df
    except Exception as e:
        logger.error(f"[ERROR] Failed to load Greeks data: {e}")
        return None
