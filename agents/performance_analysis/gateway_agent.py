#!/usr/bin/env python3
"""
🚀 Options Performance Analysis Gateway Agent

A lightweight gateway that routes queries to specialized modules and returns results.
This agent acts as a simple orchestrator without heavy business logic.

Architecture:
- Receives queries/requests
- Routes to appropriate modules
- Returns formatted responses
- Handles module lifecycle
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from enum import Enum

# Core utilities
from utils.heartbeat import create_heartbeat

logger = logging.getLogger(__name__)

class QueryType(Enum):
    """Supported query types"""
    TRADE_EVALUATION = "trade_evaluation"
    STRATEGY_METRICS = "strategy_metrics"
    MODEL_MONITORING = "model_monitoring"
    REGIME_ANALYSIS = "regime_analysis"
    RISK_FEEDBACK = "risk_feedback"
    PORTFOLIO_SUMMARY = "portfolio_summary"
    NATURAL_LANGUAGE = "natural_language"
    EXPORT_DATA = "export_data"
    HEALTH_CHECK = "health_check"

class PerformanceAnalysisGateway:
    """
    Lightweight gateway for performance analysis operations
    
    Routes queries to specialized modules and returns results
    """
    
    def __init__(self, config_path: str = "config/options_performance_analysis_config.yaml"):
        self.config_path = Path(config_path)
        self.config = None
        self.is_running = False
        
        # Module registry (lazy loaded)
        self._modules = {}
        self._module_status = {}
        
        # Core components
        self.heartbeat = create_heartbeat('performance_gateway')
        
        logger.info("🚀 Performance Analysis Gateway initialized")
    
    async def initialize(self) -> bool:
        """Initialize the gateway"""
        try:
            logger.info("[INIT] Initializing Performance Analysis Gateway...")
            
            # Load configuration
            await self._load_config()
            
            # Start heartbeat
            self.heartbeat.start_heartbeat()
            self.is_running = True
            
            logger.info("[SUCCESS] Gateway initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize gateway: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration manager"""
        try:
            from agents.performance_analysis.config_manager import OptionsPerformanceConfig
            config_manager = OptionsPerformanceConfig(self.config_path)
            success = await config_manager.load_config()
            
            if not success or not config_manager.validate_config():
                raise RuntimeError("Configuration loading/validation failed")
            
            self.config = config_manager
            logger.info("✅ Configuration loaded")
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise
    
    async def process_query(self, query_type: Union[QueryType, str], 
                          query_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Main entry point - process any query and return results
        
        Args:
            query_type: Type of query to process
            query_data: Query parameters and data
            
        Returns:
            Dict containing query results
        """
        try:
            start_time = datetime.now()
            
            # Convert string to enum if needed
            if isinstance(query_type, str):
                query_type = QueryType(query_type)
            
            logger.info(f"[QUERY] Processing {query_type.value} query...")
            
            # Route to appropriate handler
            result = await self._route_query(query_type, query_data or {})
            
            # Add metadata
            duration = (datetime.now() - start_time).total_seconds()
            result["_metadata"] = {
                "query_type": query_type.value,
                "processing_time": duration,
                "timestamp": datetime.now().isoformat(),
                "status": "success"
            }
            
            logger.info(f"[SUCCESS] Query processed in {duration:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"[ERROR] Query processing failed: {e}")
            return {
                "error": str(e),
                "_metadata": {
                    "query_type": query_type.value if isinstance(query_type, QueryType) else str(query_type),
                    "status": "error",
                    "timestamp": datetime.now().isoformat()
                }
            }
    
    async def _route_query(self, query_type: QueryType, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Route query to appropriate module"""
        
        # Route based on query type
        if query_type == QueryType.TRADE_EVALUATION:
            return await self._handle_trade_evaluation(query_data)
        
        elif query_type == QueryType.STRATEGY_METRICS:
            return await self._handle_strategy_metrics(query_data)
        
        elif query_type == QueryType.MODEL_MONITORING:
            return await self._handle_model_monitoring(query_data)
        
        elif query_type == QueryType.REGIME_ANALYSIS:
            return await self._handle_regime_analysis(query_data)
        
        elif query_type == QueryType.RISK_FEEDBACK:
            return await self._handle_risk_feedback(query_data)
        
        elif query_type == QueryType.PORTFOLIO_SUMMARY:
            return await self._handle_portfolio_summary(query_data)
        
        elif query_type == QueryType.NATURAL_LANGUAGE:
            return await self._handle_natural_language(query_data)
        
        elif query_type == QueryType.EXPORT_DATA:
            return await self._handle_export_data(query_data)
        
        elif query_type == QueryType.HEALTH_CHECK:
            return await self._handle_health_check(query_data)
        
        else:
            raise ValueError(f"Unsupported query type: {query_type}")
    
    async def _get_module(self, module_name: str):
        """Get or load a module (lazy loading)"""
        if module_name not in self._modules:
            try:
                if module_name == "trade_evaluator":
                    from agents.performance_analysis.trade_evaluator import TradeEvaluator
                    self._modules[module_name] = TradeEvaluator(self.config)
                
                elif module_name == "strategy_aggregator":
                    from agents.performance_analysis.strategy_aggregator import StrategyAggregator
                    self._modules[module_name] = StrategyAggregator(self.config)
                
                elif module_name == "model_monitor":
                    from agents.performance_analysis.model_monitoring import ModelPerformanceMonitor
                    module = ModelPerformanceMonitor(self.config)
                    await module.initialize()
                    self._modules[module_name] = module
                
                elif module_name == "regime_analyzer":
                    from agents.performance_analysis.regime_analyzer import RegimeAnalyzer
                    self._modules[module_name] = RegimeAnalyzer(self.config)
                
                elif module_name == "risk_feedback":
                    from agents.performance_analysis.risk_feedback import RiskFeedbackAnalyzer
                    self._modules[module_name] = RiskFeedbackAnalyzer(self.config)
                
                else:
                    raise ValueError(f"Unknown module: {module_name}")
                
                self._module_status[module_name] = "loaded"
                logger.debug(f"Module {module_name} loaded successfully")
                
            except Exception as e:
                logger.error(f"Failed to load module {module_name}: {e}")
                self._module_status[module_name] = "error"
                raise
        
        return self._modules[module_name]
    
    async def _load_trade_data(self):
        """Load trade data"""
        try:
            from agents.performance_analysis.data_loader import load_trade_data
            trades_path = Path(self.config.data_paths.trades_data)
            return await load_trade_data(trades_path)
        except Exception as e:
            logger.error(f"Failed to load trade data: {e}")
            return None
    
    # Query Handlers
    async def _handle_trade_evaluation(self, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle trade evaluation queries"""
        evaluator = await self._get_module("trade_evaluator")
        
        # Load trade data if not provided
        trades_df = query_data.get("trades_df")
        if trades_df is None:
            trades_df = await self._load_trade_data()
            if trades_df is None or trades_df.is_empty():
                return {"error": "No trade data available"}
        
        # Get paths
        historical_path = Path(self.config.data_paths.historical_data)
        greeks_path = Path(self.config.data_paths.greeks_data)
        
        # Evaluate trades
        evaluated_trades = await evaluator.evaluate_trade_level_performance(
            trades_df, historical_path, greeks_path
        )
        
        # Get statistics
        stats = await evaluator.calculate_trade_statistics(evaluated_trades)
        insights = await evaluator.get_trade_insights(evaluated_trades)
        
        return {
            "evaluated_trades_count": evaluated_trades.height,
            "statistics": stats,
            "insights": insights,
            "data": evaluated_trades.to_dicts() if query_data.get("include_data") else None
        }
    
    async def _handle_strategy_metrics(self, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle strategy metrics queries"""
        aggregator = await self._get_module("strategy_aggregator")
        
        # Load and evaluate trade data
        trades_df = await self._load_trade_data()
        if trades_df is None or trades_df.is_empty():
            return {"error": "No trade data available"}
        
        evaluator = await self._get_module("trade_evaluator")
        historical_path = Path(self.config.data_paths.historical_data)
        greeks_path = Path(self.config.data_paths.greeks_data)
        
        evaluated_trades = await evaluator.evaluate_trade_level_performance(
            trades_df, historical_path, greeks_path
        )
        
        # Get period from query
        period = query_data.get("period", "daily")
        
        # Aggregate metrics
        strategy_metrics = await aggregator.aggregate_strategy_metrics(evaluated_trades, period)
        portfolio_metrics = await aggregator.calculate_portfolio_metrics(evaluated_trades)
        insights = await aggregator.get_strategy_insights(strategy_metrics)
        
        return {
            "strategy_metrics": strategy_metrics,
            "portfolio_metrics": portfolio_metrics,
            "insights": insights,
            "period": period
        }
    
    async def _handle_model_monitoring(self, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle model monitoring queries"""
        monitor = await self._get_module("model_monitor")
        
        # Get prediction and actual data
        predictions_df = query_data.get("predictions_df")
        actual_df = query_data.get("actual_df")
        
        if predictions_df is None or actual_df is None:
            return {"error": "Model predictions and actual data required"}
        
        # Monitor performance
        performance_metrics = await monitor.monitor_model_performance(predictions_df, actual_df)
        health_score = await monitor.get_model_health_score(performance_metrics)
        
        return {
            "performance_metrics": performance_metrics,
            "health_score": health_score
        }
    
    async def _handle_regime_analysis(self, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle regime analysis queries"""
        analyzer = await self._get_module("regime_analyzer")
        
        # Load and evaluate trade data
        trades_df = await self._load_trade_data()
        if trades_df is None or trades_df.is_empty():
            return {"error": "No trade data available"}
        
        evaluator = await self._get_module("trade_evaluator")
        historical_path = Path(self.config.data_paths.historical_data)
        greeks_path = Path(self.config.data_paths.greeks_data)
        
        evaluated_trades = await evaluator.evaluate_trade_level_performance(
            trades_df, historical_path, greeks_path
        )
        
        # Analyze regimes
        regime_insights = await analyzer.evaluate_regime_performance(evaluated_trades)
        regime_summary = await analyzer.get_regime_summary(regime_insights)
        
        return {
            "regime_insights": regime_insights,
            "regime_summary": regime_summary
        }
    
    async def _handle_risk_feedback(self, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle risk feedback queries"""
        risk_analyzer = await self._get_module("risk_feedback")
        
        # Load and evaluate trade data
        trades_df = await self._load_trade_data()
        if trades_df is None or trades_df.is_empty():
            return {"error": "No trade data available"}
        
        evaluator = await self._get_module("trade_evaluator")
        historical_path = Path(self.config.data_paths.historical_data)
        greeks_path = Path(self.config.data_paths.greeks_data)
        
        evaluated_trades = await evaluator.evaluate_trade_level_performance(
            trades_df, historical_path, greeks_path
        )
        
        # Analyze risk feedback
        risk_feedback = await risk_analyzer.provide_risk_control_feedback(evaluated_trades)
        
        return risk_feedback

    async def _handle_portfolio_summary(self, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle portfolio summary queries"""
        # Get multiple modules for comprehensive summary
        evaluator = await self._get_module("trade_evaluator")
        aggregator = await self._get_module("strategy_aggregator")

        # Load and evaluate trade data
        trades_df = await self._load_trade_data()
        if trades_df is None or trades_df.is_empty():
            return {"error": "No trade data available"}

        historical_path = Path(self.config.data_paths.historical_data)
        greeks_path = Path(self.config.data_paths.greeks_data)

        evaluated_trades = await evaluator.evaluate_trade_level_performance(
            trades_df, historical_path, greeks_path
        )

        # Get summary metrics
        trade_stats = await evaluator.calculate_trade_statistics(evaluated_trades)
        portfolio_metrics = await aggregator.calculate_portfolio_metrics(evaluated_trades)

        return {
            "trade_statistics": trade_stats,
            "portfolio_metrics": portfolio_metrics,
            "summary": {
                "total_trades": trade_stats.get("total_trades", 0),
                "win_rate": trade_stats.get("win_rate", 0),
                "total_pnl": trade_stats.get("total_pnl", 0),
                "best_trade": trade_stats.get("best_trade", 0),
                "worst_trade": trade_stats.get("worst_trade", 0)
            }
        }

    async def _handle_natural_language(self, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle natural language queries"""
        query_text = query_data.get("query", "").lower()

        # Simple keyword-based routing
        if any(word in query_text for word in ["win rate", "winning", "success"]):
            result = await self._handle_portfolio_summary({})
            return {
                "answer": f"Your current win rate is {result['summary']['win_rate']}%",
                "details": result
            }

        elif any(word in query_text for word in ["pnl", "profit", "loss", "money"]):
            result = await self._handle_portfolio_summary({})
            return {
                "answer": f"Your total P&L is ₹{result['summary']['total_pnl']:.2f}",
                "details": result
            }

        elif any(word in query_text for word in ["risk", "drawdown", "danger"]):
            result = await self._handle_risk_feedback({})
            return {
                "answer": f"Risk analysis complete. Overall risk level: {result.get('risk_score', {}).get('risk_level', 'Unknown')}",
                "details": result
            }

        elif any(word in query_text for word in ["strategy", "performance", "metrics"]):
            result = await self._handle_strategy_metrics({})
            return {
                "answer": f"Strategy analysis complete for {len(result['strategy_metrics'])} strategies",
                "details": result
            }

        else:
            return {
                "answer": "I can help you with win rates, P&L, risk analysis, and strategy performance. What would you like to know?",
                "suggestions": [
                    "What is my win rate?",
                    "How much profit have I made?",
                    "What is my risk level?",
                    "How are my strategies performing?"
                ]
            }

    async def _handle_export_data(self, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle data export queries"""
        export_format = query_data.get("format", "json")
        export_type = query_data.get("type", "portfolio_summary")

        # Get data based on export type
        if export_type == "portfolio_summary":
            data = await self._handle_portfolio_summary({})
        elif export_type == "strategy_metrics":
            data = await self._handle_strategy_metrics({})
        elif export_type == "risk_feedback":
            data = await self._handle_risk_feedback({})
        else:
            return {"error": f"Unsupported export type: {export_type}"}

        # Export data
        export_path = Path(self.config.data_paths.export_path)
        export_path.mkdir(exist_ok=True)

        filename = f"{export_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{export_format}"
        filepath = export_path / filename

        if export_format == "json":
            import json
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, default=str)

        return {
            "exported_file": str(filepath),
            "format": export_format,
            "type": export_type,
            "size": filepath.stat().st_size if filepath.exists() else 0
        }

    async def _handle_health_check(self, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle health check queries"""
        return {
            "status": "healthy" if self.is_running else "stopped",
            "modules_loaded": list(self._modules.keys()),
            "module_status": self._module_status,
            "config_loaded": self.config is not None,
            "heartbeat_active": self.heartbeat.is_active() if hasattr(self.heartbeat, 'is_active') else True,
            "timestamp": datetime.now().isoformat()
        }

    # Convenience methods for common operations
    async def get_win_rate(self) -> float:
        """Quick method to get win rate"""
        result = await self.process_query(QueryType.PORTFOLIO_SUMMARY)
        return result.get("summary", {}).get("win_rate", 0)

    async def get_total_pnl(self) -> float:
        """Quick method to get total P&L"""
        result = await self.process_query(QueryType.PORTFOLIO_SUMMARY)
        return result.get("summary", {}).get("total_pnl", 0)

    async def get_risk_level(self) -> str:
        """Quick method to get risk level"""
        result = await self.process_query(QueryType.RISK_FEEDBACK)
        return result.get("risk_score", {}).get("risk_level", "Unknown")

    async def ask(self, question: str) -> str:
        """Natural language interface"""
        result = await self.process_query(QueryType.NATURAL_LANGUAGE, {"query": question})
        return result.get("answer", "I couldn't understand your question.")

    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("[CLEANUP] Cleaning up Performance Analysis Gateway...")
            self.is_running = False

            # Stop heartbeat
            if hasattr(self, 'heartbeat') and self.heartbeat:
                self.heartbeat.stop_heartbeat()

            # Clear modules
            self._modules.clear()
            self._module_status.clear()

            logger.info("[SUCCESS] Gateway cleaned up")

        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")

# Example usage
async def main():
    """Example usage of the gateway"""
    gateway = PerformanceAnalysisGateway()

    try:
        # Initialize
        await gateway.initialize()

        # Example queries
        print("🔍 Getting portfolio summary...")
        portfolio = await gateway.process_query(QueryType.PORTFOLIO_SUMMARY)
        print(f"Win Rate: {portfolio['summary']['win_rate']}%")

        print("\n💬 Natural language query...")
        answer = await gateway.ask("What is my win rate?")
        print(f"Answer: {answer}")

        print("\n🏥 Health check...")
        health = await gateway.process_query(QueryType.HEALTH_CHECK)
        print(f"Status: {health['status']}")

    except KeyboardInterrupt:
        logger.info("Gateway interrupted by user. 🛑")
    finally:
        await gateway.cleanup()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
