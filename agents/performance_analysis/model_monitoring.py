#!/usr/bin/env python3
"""
Model Performance Monitoring Module for Options Performance Analysis Agent

This module handles:
- AI model prediction accuracy tracking
- Drift detection and alerting
- Confidence calibration analysis
- Retraining recommendations
- Model performance degradation monitoring
"""

import asyncio
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import polars as pl
import json

# Lazy imports for performance
def lazy_import(name):
    import importlib.util
    spec = importlib.util.find_spec(name)
    if spec is None:
        return None
    loader = importlib.util.LazyLoader(spec.loader)
    spec.loader = loader
    module = importlib.util.module_from_spec(spec)
    return module

np = lazy_import("numpy")
stats = lazy_import("scipy.stats")

logger = logging.getLogger(__name__)

class ModelPerformanceMonitor:
    """Model performance monitoring and drift detection engine"""

    def __init__(self, config=None):
        self.config = config
        self.historical_baselines = {}
        self.drift_thresholds = {
            'accuracy_drop': 0.10,      # 10% accuracy drop
            'confidence_drift': 0.15,   # 15% confidence drift
            'roi_prediction_error': 0.20 # 20% ROI prediction error increase
        }
        self.baseline_file = Path("data/performance/model_baselines.json")

    async def initialize(self):
        """Initialize the monitor by loading historical baselines"""
        await self._load_historical_baselines()

    async def monitor_model_performance(self, model_predictions_df: pl.DataFrame,
                                      actual_trades_df: pl.DataFrame) -> Dict[str, Any]:
        """
        Monitors AI model predictions in live mode.
        Calculates accuracy, predicted vs actual ROI, confidence calibration.
        Detects drift and recommends retraining.
        """
        if model_predictions_df.is_empty() or actual_trades_df.is_empty():
            logger.warning("No data for model performance monitoring.")
            return {"status": "No data"}

        logger.info("[ANALYZE] Monitoring model performance...")

        # Merge predictions with actual outcomes
        merged_df = await self._merge_predictions_with_outcomes(model_predictions_df, actual_trades_df)

        if merged_df.is_empty():
            logger.warning("No matching trades for model performance monitoring after merge.")
            return {"status": "No matching data"}

        # Calculate performance metrics
        performance_metrics = await self._calculate_performance_metrics(merged_df)

        # Detect drift
        drift_analysis = await self._detect_drift(performance_metrics)

        # Generate recommendations
        recommendations = await self._generate_recommendations(performance_metrics, drift_analysis)

        # Update baselines if needed
        await self._update_baselines(performance_metrics)

        return {
            **performance_metrics,
            **drift_analysis,
            "recommendations": recommendations,
            "timestamp": datetime.now().isoformat()
        }

    async def _merge_predictions_with_outcomes(self, predictions_df: pl.DataFrame,
                                             actual_df: pl.DataFrame) -> pl.DataFrame:
        """Merge model predictions with actual trade outcomes"""
        try:
            # Merge on trade_id or similar identifier
            merged_df = predictions_df.join(actual_df, on="trade_id", how="inner")
            logger.info(f"Successfully merged {merged_df.height} prediction-outcome pairs")
            return merged_df
        except Exception as e:
            logger.error(f"Error merging predictions with outcomes: {e}")
            return pl.DataFrame()

    async def _calculate_performance_metrics(self, merged_df: pl.DataFrame) -> Dict[str, Any]:
        """Calculate comprehensive model performance metrics"""
        metrics = {}

        # Direction accuracy
        direction_accuracy = (pl.col("predicted_direction") == pl.col("actual_direction")).mean()
        metrics["direction_accuracy"] = merged_df.select(direction_accuracy).item()

        # ROI prediction accuracy (Mean Absolute Error)
        roi_mae = (pl.col("predicted_roi") - pl.col("roi_percent")).abs().mean()
        metrics["roi_prediction_mae"] = merged_df.select(roi_mae).item()

        # ROI prediction correlation
        if stats is not None:
            predicted_roi = merged_df.select("predicted_roi").to_series().to_numpy()
            actual_roi = merged_df.select("roi_percent").to_series().to_numpy()

            if len(predicted_roi) > 1:
                correlation, p_value = stats.pearsonr(predicted_roi, actual_roi)
                metrics["roi_prediction_correlation"] = correlation
                metrics["roi_correlation_p_value"] = p_value
            else:
                metrics["roi_prediction_correlation"] = 0.0
                metrics["roi_correlation_p_value"] = 1.0

        # Confidence calibration
        confidence_metrics = await self._analyze_confidence_calibration(merged_df)
        metrics.update(confidence_metrics)

        # Prediction distribution analysis
        distribution_metrics = await self._analyze_prediction_distributions(merged_df)
        metrics.update(distribution_metrics)

        return metrics

    async def _analyze_confidence_calibration(self, merged_df: pl.DataFrame) -> Dict[str, Any]:
        """Analyze how well model confidence correlates with actual outcomes"""
        calibration_metrics = {}

        try:
            # Bin predictions by confidence levels
            confidence_bins = [0.0, 0.5, 0.7, 0.8, 0.9, 1.0]

            for i in range(len(confidence_bins) - 1):
                bin_start, bin_end = confidence_bins[i], confidence_bins[i + 1]

                bin_data = merged_df.filter(
                    (pl.col("prediction_confidence") >= bin_start) &
                    (pl.col("prediction_confidence") < bin_end)
                )

                if not bin_data.is_empty():
                    bin_accuracy = bin_data.select(
                        (pl.col("predicted_direction") == pl.col("actual_direction")).mean()
                    ).item()

                    calibration_metrics[f"accuracy_confidence_{bin_start}_{bin_end}"] = bin_accuracy

            # Overall confidence vs accuracy correlation
            if not merged_df.is_empty():
                avg_confidence = merged_df.select(pl.col("prediction_confidence").mean()).item()
                calibration_metrics["average_confidence"] = avg_confidence

                # Calculate confidence-outcome correlation
                high_conf_accuracy = merged_df.filter(pl.col("prediction_confidence") > 0.7).select(
                    (pl.col("predicted_direction") == pl.col("actual_direction")).mean()
                ).item() if not merged_df.filter(pl.col("prediction_confidence") > 0.7).is_empty() else 0.0

                low_conf_accuracy = merged_df.filter(pl.col("prediction_confidence") <= 0.5).select(
                    (pl.col("predicted_direction") == pl.col("actual_direction")).mean()
                ).item() if not merged_df.filter(pl.col("prediction_confidence") <= 0.5).is_empty() else 0.0

                calibration_metrics["high_confidence_accuracy"] = high_conf_accuracy
                calibration_metrics["low_confidence_accuracy"] = low_conf_accuracy
                calibration_metrics["confidence_discrimination"] = high_conf_accuracy - low_conf_accuracy

        except Exception as e:
            logger.error(f"Error analyzing confidence calibration: {e}")
            calibration_metrics["error"] = str(e)

        return calibration_metrics

    async def _analyze_prediction_distributions(self, merged_df: pl.DataFrame) -> Dict[str, Any]:
        """Analyze prediction distributions and patterns"""
        distribution_metrics = {}

        try:
            # Prediction confidence distribution
            confidence_stats = merged_df.select([
                pl.col("prediction_confidence").mean().alias("mean_confidence"),
                pl.col("prediction_confidence").std().alias("std_confidence"),
                pl.col("prediction_confidence").min().alias("min_confidence"),
                pl.col("prediction_confidence").max().alias("max_confidence")
            ]).row(0, named=True)

            distribution_metrics.update({
                "mean_prediction_confidence": confidence_stats["mean_confidence"],
                "std_prediction_confidence": confidence_stats["std_confidence"],
                "min_prediction_confidence": confidence_stats["min_confidence"],
                "max_prediction_confidence": confidence_stats["max_confidence"]
            })

            # ROI prediction distribution
            roi_stats = merged_df.select([
                pl.col("predicted_roi").mean().alias("mean_predicted_roi"),
                pl.col("predicted_roi").std().alias("std_predicted_roi"),
                pl.col("roi_percent").mean().alias("mean_actual_roi"),
                pl.col("roi_percent").std().alias("std_actual_roi")
            ]).row(0, named=True)

            distribution_metrics.update({
                "mean_predicted_roi": roi_stats["mean_predicted_roi"],
                "std_predicted_roi": roi_stats["std_predicted_roi"],
                "mean_actual_roi": roi_stats["mean_actual_roi"],
                "std_actual_roi": roi_stats["std_actual_roi"]
            })

            # Direction prediction distribution
            direction_counts = merged_df.group_by("predicted_direction").agg([
                pl.len().alias("count")
            ])

            total_predictions = merged_df.height
            for row in direction_counts.iter_rows(named=True):
                direction = row["predicted_direction"]
                count = row["count"]
                distribution_metrics[f"predicted_{direction}_ratio"] = count / total_predictions

        except Exception as e:
            logger.error(f"Error analyzing prediction distributions: {e}")
            distribution_metrics["error"] = str(e)

        return distribution_metrics

    async def _detect_drift(self, current_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Detect model performance drift"""
        drift_analysis = {
            "drift_detected": False,
            "drift_alerts": [],
            "drift_scores": {}
        }

        try:
            # Load historical baselines
            if not self.historical_baselines:
                await self._load_historical_baselines()

            # Check accuracy drift
            if "direction_accuracy_baseline" in self.historical_baselines:
                baseline_accuracy = self.historical_baselines["direction_accuracy_baseline"]
                current_accuracy = current_metrics.get("direction_accuracy", 0)
                accuracy_drift = baseline_accuracy - current_accuracy

                drift_analysis["drift_scores"]["accuracy_drift"] = accuracy_drift

                if accuracy_drift > self.drift_thresholds["accuracy_drop"]:
                    drift_analysis["drift_detected"] = True
                    drift_analysis["drift_alerts"].append(
                        f"Accuracy drift detected: {baseline_accuracy:.3f} → {current_accuracy:.3f} "
                        f"(drop: {accuracy_drift:.3f})"
                    )

            # Check ROI prediction drift
            if "roi_mae_baseline" in self.historical_baselines:
                baseline_mae = self.historical_baselines["roi_mae_baseline"]
                current_mae = current_metrics.get("roi_prediction_mae", 0)
                mae_increase = (current_mae - baseline_mae) / baseline_mae if baseline_mae > 0 else 0

                drift_analysis["drift_scores"]["roi_mae_drift"] = mae_increase

                if mae_increase > self.drift_thresholds["roi_prediction_error"]:
                    drift_analysis["drift_detected"] = True
                    drift_analysis["drift_alerts"].append(
                        f"ROI prediction error increased: {baseline_mae:.3f} → {current_mae:.3f} "
                        f"(increase: {mae_increase:.1%})"
                    )

            # Check confidence calibration drift
            if "confidence_discrimination_baseline" in self.historical_baselines:
                baseline_discrimination = self.historical_baselines["confidence_discrimination_baseline"]
                current_discrimination = current_metrics.get("confidence_discrimination", 0)
                discrimination_drift = baseline_discrimination - current_discrimination

                drift_analysis["drift_scores"]["confidence_drift"] = discrimination_drift

                if abs(discrimination_drift) > self.drift_thresholds["confidence_drift"]:
                    drift_analysis["drift_detected"] = True
                    drift_analysis["drift_alerts"].append(
                        f"Confidence calibration drift: {baseline_discrimination:.3f} → {current_discrimination:.3f} "
                        f"(change: {discrimination_drift:.3f})"
                    )

        except Exception as e:
            logger.error(f"Error detecting drift: {e}")
            drift_analysis["error"] = str(e)

        return drift_analysis

    async def _generate_recommendations(self, performance_metrics: Dict[str, Any],
                                      drift_analysis: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations based on model performance"""
        recommendations = []

        try:
            # Retraining recommendations
            if drift_analysis.get("drift_detected", False):
                recommendations.append("🔄 Model retraining recommended due to detected performance drift")

                # Specific recommendations based on drift type
                drift_scores = drift_analysis.get("drift_scores", {})

                if drift_scores.get("accuracy_drift", 0) > 0.05:
                    recommendations.append("📉 Focus on improving directional prediction accuracy")

                if drift_scores.get("roi_mae_drift", 0) > 0.1:
                    recommendations.append("💰 Enhance ROI prediction model with recent market data")

                if abs(drift_scores.get("confidence_drift", 0)) > 0.1:
                    recommendations.append("🎯 Recalibrate confidence scoring mechanism")

            # Performance improvement recommendations
            direction_accuracy = performance_metrics.get("direction_accuracy", 0)
            if direction_accuracy < 0.55:
                recommendations.append("⚠️ Direction accuracy below 55%. Consider feature engineering or model architecture changes")

            roi_correlation = performance_metrics.get("roi_prediction_correlation", 0)
            if roi_correlation < 0.3:
                recommendations.append("📊 Low ROI prediction correlation. Review target variable and feature selection")

            confidence_discrimination = performance_metrics.get("confidence_discrimination", 0)
            if confidence_discrimination < 0.1:
                recommendations.append("🎲 Poor confidence calibration. Model confidence not discriminative")

            # Data quality recommendations
            if performance_metrics.get("std_prediction_confidence", 1) < 0.1:
                recommendations.append("📏 Low confidence variance suggests overconfident model")

            # Operational recommendations
            if len(drift_analysis.get("drift_alerts", [])) > 2:
                recommendations.append("🚨 Multiple drift signals detected. Immediate model review required")

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            recommendations.append(f"❌ Error generating recommendations: {str(e)}")

        return recommendations

    async def _load_historical_baselines(self):
        """Load historical performance baselines"""
        try:
            if self.baseline_file.exists():
                with open(self.baseline_file, 'r') as f:
                    self.historical_baselines = json.load(f)
                logger.info(f"Loaded historical baselines from {self.baseline_file}")
            else:
                # Set default baselines
                self.historical_baselines = {
                    "direction_accuracy_baseline": 0.65,
                    "roi_mae_baseline": 0.05,
                    "confidence_discrimination_baseline": 0.15,
                    "last_updated": datetime.now().isoformat()
                }
                logger.info("Using default baselines - no historical data found")
        except Exception as e:
            logger.error(f"Error loading historical baselines: {e}")
            self.historical_baselines = {}

    async def _update_baselines(self, current_metrics: Dict[str, Any]):
        """Update historical baselines with current performance"""
        try:
            # Update baselines with exponential moving average
            alpha = 0.1  # Learning rate for baseline updates

            current_accuracy = current_metrics.get("direction_accuracy", 0)
            if current_accuracy > 0:
                if "direction_accuracy_baseline" in self.historical_baselines:
                    self.historical_baselines["direction_accuracy_baseline"] = (
                        alpha * current_accuracy +
                        (1 - alpha) * self.historical_baselines["direction_accuracy_baseline"]
                    )
                else:
                    self.historical_baselines["direction_accuracy_baseline"] = current_accuracy

            current_mae = current_metrics.get("roi_prediction_mae", 0)
            if current_mae > 0:
                if "roi_mae_baseline" in self.historical_baselines:
                    self.historical_baselines["roi_mae_baseline"] = (
                        alpha * current_mae +
                        (1 - alpha) * self.historical_baselines["roi_mae_baseline"]
                    )
                else:
                    self.historical_baselines["roi_mae_baseline"] = current_mae

            current_discrimination = current_metrics.get("confidence_discrimination", 0)
            if "confidence_discrimination_baseline" in self.historical_baselines:
                self.historical_baselines["confidence_discrimination_baseline"] = (
                    alpha * current_discrimination +
                    (1 - alpha) * self.historical_baselines["confidence_discrimination_baseline"]
                )
            else:
                self.historical_baselines["confidence_discrimination_baseline"] = current_discrimination

            # Update timestamp
            self.historical_baselines["last_updated"] = datetime.now().isoformat()

            # Save updated baselines
            self.baseline_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.baseline_file, 'w') as f:
                json.dump(self.historical_baselines, f, indent=2)

            logger.debug("Updated historical baselines")

        except Exception as e:
            logger.error(f"Error updating baselines: {e}")

    async def get_model_health_score(self, performance_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall model health score"""
        try:
            health_components = {}

            # Direction accuracy component (0-100)
            direction_accuracy = performance_metrics.get("direction_accuracy", 0)
            health_components["accuracy_score"] = min(100, direction_accuracy * 100 * 1.5)  # Scale up good accuracy

            # ROI prediction component (0-100)
            roi_mae = performance_metrics.get("roi_prediction_mae", 1)
            health_components["roi_score"] = max(0, 100 - roi_mae * 1000)  # Lower MAE = higher score

            # Confidence calibration component (0-100)
            confidence_discrimination = performance_metrics.get("confidence_discrimination", 0)
            health_components["confidence_score"] = min(100, confidence_discrimination * 500)  # Scale up discrimination

            # Overall health score (weighted average)
            weights = {"accuracy_score": 0.5, "roi_score": 0.3, "confidence_score": 0.2}
            overall_score = sum(health_components[component] * weights[component]
                              for component in health_components)

            # Health status
            if overall_score >= 80:
                health_status = "Excellent"
            elif overall_score >= 60:
                health_status = "Good"
            elif overall_score >= 40:
                health_status = "Fair"
            else:
                health_status = "Poor"

            return {
                "overall_health_score": round(overall_score, 1),
                "health_status": health_status,
                "component_scores": {k: round(v, 1) for k, v in health_components.items()},
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error calculating model health score: {e}")
            return {"error": str(e)}
