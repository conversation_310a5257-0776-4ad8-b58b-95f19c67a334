#!/usr/bin/env python3
"""
🚀 Refactored Options Performance Analysis Agent - Modular Architecture

This is the main agent class that orchestrates all performance analysis modules.
Reduced from 1593 lines to ~600-800 lines through modularization.

Key Features:
- Modular architecture with lazy loading
- Comprehensive configuration management
- Real-time performance monitoring
- Advanced analytics and insights
- Export capabilities (Excel, JSON)
- Natural language query interface
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import warnings

# Core utilities
from utils.heartbeat import create_heartbeat

# Lazy loading for performance optimization
def lazy_import(name):
    import importlib.util
    spec = importlib.util.find_spec(name)
    if spec is None:
        return None
    loader = importlib.util.LazyLoader(spec.loader)
    spec.loader = loader
    module = importlib.util.module_from_spec(spec)
    return module

# Lazy imports for heavy dependencies
np = lazy_import("numpy")
pd = lazy_import("pandas")
win10toast = lazy_import("win10toast")

# Performance analysis modules (lazy loaded)
_config_manager = None
_trade_evaluator = None
_strategy_aggregator = None
_model_monitor = None
_regime_analyzer = None
_risk_feedback = None

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)

class RefactoredOptionsPerformanceAnalysisAgent:
    """
    Refactored Options Performance Analysis Agent with modular architecture
    
    This agent orchestrates various analysis modules to provide comprehensive
    performance analytics for options trading strategies.
    """
    
    def __init__(self, config_path: str = "config/options_performance_analysis_config.yaml"):
        self.config_path = Path(config_path)
        self.config = None
        self.is_running = False
        
        # Module instances (lazy loaded)
        self._config_manager = None
        self._trade_evaluator = None
        self._strategy_aggregator = None
        self._model_monitor = None
        self._regime_analyzer = None
        self._risk_feedback = None
        
        # Core components
        self.heartbeat = create_heartbeat('performance_analysis')
        self.toaster = None
        
        # Performance tracking
        self.analysis_stats = {
            "total_analyses": 0,
            "last_analysis_time": None,
            "avg_analysis_duration": 0
        }
        
        logger.info("🚀 Refactored Options Performance Analysis Agent initialized")
    
    async def initialize(self, **kwargs) -> bool:
        """Initialize the agent and all its modules"""
        try:
            start_time = datetime.now()
            logger.info("[INIT] Initializing Refactored Performance Analysis Agent...")
            
            # Load configuration first
            await self._load_configuration()
            
            # Initialize Windows notifications if enabled
            await self._initialize_notifications()
            
            # Initialize modules (lazy loaded)
            await self._initialize_modules()
            
            # Start heartbeat
            self.heartbeat.start_heartbeat()
            
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"[SUCCESS] Agent initialized successfully in {duration:.2f}s")
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_configuration(self):
        """Load and validate configuration"""
        global _config_manager
        if _config_manager is None:
            from agents.performance_analysis.config_manager import OptionsPerformanceConfig
            _config_manager = OptionsPerformanceConfig
        
        self._config_manager = _config_manager(self.config_path)
        success = await self._config_manager.load_config()
        
        if not success:
            raise RuntimeError("Failed to load configuration")
        
        if not self._config_manager.validate_config():
            raise RuntimeError("Configuration validation failed")
        
        self.config = self._config_manager
        logger.info("✅ Configuration loaded and validated")
    
    async def _initialize_notifications(self):
        """Initialize notification system"""
        if (self.config.notification_settings.enable_windows_notifications and 
            win10toast is not None):
            try:
                self.toaster = win10toast.ToastNotifier()
                logger.info("📢 Windows notifications enabled")
            except Exception as e:
                logger.warning(f"Failed to initialize notifications: {e}")
    
    async def _initialize_modules(self):
        """Initialize all analysis modules with lazy loading"""
        try:
            # Trade Evaluator
            global _trade_evaluator
            if _trade_evaluator is None:
                from agents.performance_analysis.trade_evaluator import TradeEvaluator
                _trade_evaluator = TradeEvaluator
            self._trade_evaluator = _trade_evaluator(self.config)
            
            # Strategy Aggregator
            global _strategy_aggregator
            if _strategy_aggregator is None:
                from agents.performance_analysis.strategy_aggregator import StrategyAggregator
                _strategy_aggregator = StrategyAggregator
            self._strategy_aggregator = _strategy_aggregator(self.config)
            
            # Model Monitor
            global _model_monitor
            if _model_monitor is None:
                from agents.performance_analysis.model_monitoring import ModelPerformanceMonitor
                _model_monitor = ModelPerformanceMonitor
            self._model_monitor = _model_monitor(self.config)
            await self._model_monitor.initialize()
            
            # Regime Analyzer
            global _regime_analyzer
            if _regime_analyzer is None:
                from agents.performance_analysis.regime_analyzer import RegimeAnalyzer
                _regime_analyzer = RegimeAnalyzer
            self._regime_analyzer = _regime_analyzer(self.config)
            
            # Risk Feedback Analyzer
            global _risk_feedback
            if _risk_feedback is None:
                from agents.performance_analysis.risk_feedback import RiskFeedbackAnalyzer
                _risk_feedback = RiskFeedbackAnalyzer
            self._risk_feedback = _risk_feedback(self.config)
            
            logger.info("🔧 All analysis modules initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize modules: {e}")
            raise
    
    async def start(self, **kwargs) -> bool:
        """Start the performance analysis agent"""
        try:
            start_time = datetime.now()
            logger.info("[START] Starting comprehensive performance analysis...")
            self.is_running = True
            
            # Load trade data
            trades_df = await self._load_trade_data()
            if trades_df is None or trades_df.is_empty():
                logger.warning("No trade data available. Running in monitoring mode.")
                return True
            
            # Execute comprehensive analysis pipeline
            analysis_results = await self._execute_analysis_pipeline(trades_df)
            
            # Generate comprehensive dashboard
            dashboard = await self._generate_comprehensive_dashboard(analysis_results)
            
            # Export results
            await self._export_results(dashboard)
            
            # Send notifications if needed
            await self._send_notifications(dashboard)
            
            # Update performance statistics
            duration = (datetime.now() - start_time).total_seconds()
            await self._update_performance_stats(duration)
            
            logger.info(f"✅ Analysis completed successfully in {duration:.2f}s")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Analysis failed: {e}")
            return False
    
    async def _load_trade_data(self):
        """Load trade data using the data loader module"""
        try:
            from agents.performance_analysis.data_loader import load_trade_data
            trades_path = Path(self.config.data_paths.trades_data)
            return await load_trade_data(trades_path)
        except Exception as e:
            logger.error(f"Failed to load trade data: {e}")
            return None
    
    async def _execute_analysis_pipeline(self, trades_df) -> Dict[str, Any]:
        """Execute the complete analysis pipeline"""
        results = {}
        
        try:
            # 1. Trade-level evaluation
            logger.info("📊 Evaluating trade-level performance...")
            historical_path = Path(self.config.data_paths.historical_data)
            greeks_path = Path(self.config.data_paths.greeks_data)
            
            evaluated_trades = await self._trade_evaluator.evaluate_trade_level_performance(
                trades_df, historical_path, greeks_path
            )
            results["evaluated_trades"] = evaluated_trades
            
            # 2. Strategy aggregation
            logger.info("📈 Aggregating strategy metrics...")
            strategy_metrics_daily = await self._strategy_aggregator.aggregate_strategy_metrics(
                evaluated_trades, period="daily"
            )
            strategy_metrics_weekly = await self._strategy_aggregator.aggregate_strategy_metrics(
                evaluated_trades, period="weekly"
            )
            results["strategy_metrics"] = {
                "daily": strategy_metrics_daily,
                "weekly": strategy_metrics_weekly
            }
            
            # 3. Portfolio-level metrics
            portfolio_metrics = await self._strategy_aggregator.calculate_portfolio_metrics(evaluated_trades)
            results["portfolio_metrics"] = portfolio_metrics
            
            # 4. Regime analysis
            logger.info("🌍 Analyzing regime-based performance...")
            regime_insights = await self._regime_analyzer.evaluate_regime_performance(evaluated_trades)
            results["regime_insights"] = regime_insights
            
            # 5. Risk management feedback
            logger.info("🛡️ Analyzing risk management effectiveness...")
            risk_feedback = await self._risk_feedback.provide_risk_control_feedback(evaluated_trades)
            results["risk_feedback"] = risk_feedback
            
            # 6. Generate insights and recommendations
            results["insights"] = await self._generate_comprehensive_insights(results)
            
            return results
            
        except Exception as e:
            logger.error(f"Error in analysis pipeline: {e}")
            return {"error": str(e)}
    
    async def _generate_comprehensive_insights(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive insights from all analysis results"""
        insights = {
            "trade_insights": [],
            "strategy_insights": [],
            "regime_insights": [],
            "risk_insights": [],
            "overall_recommendations": []
        }
        
        try:
            # Trade-level insights
            if "evaluated_trades" in results:
                trade_stats = await self._trade_evaluator.calculate_trade_statistics(results["evaluated_trades"])
                insights["trade_insights"] = await self._trade_evaluator.get_trade_insights(results["evaluated_trades"])
            
            # Strategy insights
            if "strategy_metrics" in results:
                insights["strategy_insights"] = await self._strategy_aggregator.get_strategy_insights(
                    results["strategy_metrics"]["daily"]
                )
            
            # Regime insights
            if "regime_insights" in results:
                insights["regime_insights"] = results["regime_insights"].get("recommendations", [])
            
            # Risk insights
            if "risk_feedback" in results:
                insights["risk_insights"] = results["risk_feedback"].get("recommendations", [])
            
            # Overall recommendations
            insights["overall_recommendations"] = await self._generate_overall_recommendations(results)
            
        except Exception as e:
            logger.error(f"Error generating insights: {e}")
            insights["error"] = str(e)
        
        return insights

    async def _generate_overall_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Generate overall recommendations based on all analysis results"""
        recommendations = []

        try:
            # Portfolio performance recommendations
            portfolio_metrics = results.get("portfolio_metrics", {})
            win_rate = portfolio_metrics.get("win_rate", 0)
            sharpe_ratio = portfolio_metrics.get("sharpe_ratio", 0)

            if win_rate < 50:
                recommendations.append("🎯 Overall win rate below 50%. Focus on improving entry criteria and signal quality.")

            if sharpe_ratio < 1.0:
                recommendations.append("📊 Sharpe ratio below 1.0. Consider risk management improvements or strategy optimization.")

            # Risk management recommendations
            risk_feedback = results.get("risk_feedback", {})
            risk_score = risk_feedback.get("risk_score", {})
            if risk_score.get("overall_risk_score", 100) < 60:
                recommendations.append("🚨 Risk management score below 60. Immediate review of risk controls required.")

            # Strategy diversification recommendations
            strategy_metrics = results.get("strategy_metrics", {}).get("daily", {})
            if len(strategy_metrics) < 3:
                recommendations.append("🔄 Consider diversifying with additional strategies to reduce concentration risk.")

            return recommendations

        except Exception as e:
            logger.error(f"Error generating overall recommendations: {e}")
            return [f"❌ Error generating recommendations: {str(e)}"]

    async def _generate_comprehensive_dashboard(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive performance dashboard"""
        dashboard = {
            "timestamp": datetime.now().isoformat(),
            "summary": {},
            "performance_metrics": {},
            "risk_metrics": {},
            "insights": {},
            "alerts": [],
            "export_ready": True
        }

        try:
            # Summary metrics
            portfolio_metrics = results.get("portfolio_metrics", {})
            dashboard["summary"] = {
                "total_trades": portfolio_metrics.get("total_trades", 0),
                "win_rate": portfolio_metrics.get("win_rate", 0),
                "total_pnl": portfolio_metrics.get("total_pnl", 0),
                "sharpe_ratio": portfolio_metrics.get("sharpe_ratio", 0),
                "max_drawdown": portfolio_metrics.get("max_drawdown", 0)
            }

            # Performance metrics
            dashboard["performance_metrics"] = portfolio_metrics

            # Risk metrics
            risk_feedback = results.get("risk_feedback", {})
            dashboard["risk_metrics"] = risk_feedback.get("risk_score", {})

            # Insights
            dashboard["insights"] = results.get("insights", {})

            # Alerts
            dashboard["alerts"] = risk_feedback.get("alerts", [])

            # Analysis metadata
            dashboard["analysis_metadata"] = {
                "modules_used": ["trade_evaluator", "strategy_aggregator", "regime_analyzer", "risk_feedback"],
                "analysis_duration": self.analysis_stats.get("avg_analysis_duration", 0),
                "data_quality_score": await self._calculate_data_quality_score(results)
            }

        except Exception as e:
            logger.error(f"Error generating dashboard: {e}")
            dashboard["error"] = str(e)

        return dashboard

    async def _calculate_data_quality_score(self, results: Dict[str, Any]) -> float:
        """Calculate data quality score based on available data"""
        try:
            score = 0
            max_score = 100

            # Check data availability
            if "evaluated_trades" in results and not results["evaluated_trades"].is_empty():
                score += 30

            if "strategy_metrics" in results and results["strategy_metrics"]["daily"]:
                score += 25

            if "regime_insights" in results and not results["regime_insights"].get("error"):
                score += 25

            if "risk_feedback" in results and not results["risk_feedback"].get("error"):
                score += 20

            return min(100, score)

        except Exception:
            return 50  # Default score if calculation fails

    async def _export_results(self, dashboard: Dict[str, Any]):
        """Export results to configured formats"""
        try:
            export_formats = self.config.get_config_value("export_settings.default_formats", ["json"])

            for format_type in export_formats:
                if format_type == "excel" and pd is not None:
                    await self._export_to_excel(dashboard)
                elif format_type == "json":
                    await self._export_to_json(dashboard)

        except Exception as e:
            logger.error(f"Error exporting results: {e}")

    async def _export_to_excel(self, dashboard: Dict[str, Any]):
        """Export dashboard to Excel format"""
        try:
            export_path = Path(self.config.data_paths.export_path)
            export_path.mkdir(exist_ok=True)

            filename = f"performance_dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            filepath = export_path / filename

            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # Summary sheet
                summary_df = pd.DataFrame([dashboard["summary"]])
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

                # Performance metrics sheet
                perf_df = pd.DataFrame([dashboard["performance_metrics"]])
                perf_df.to_excel(writer, sheet_name='Performance', index=False)

                # Risk metrics sheet
                risk_df = pd.DataFrame([dashboard["risk_metrics"]])
                risk_df.to_excel(writer, sheet_name='Risk', index=False)

            logger.info(f"📊 Excel report exported: {filepath}")

        except Exception as e:
            logger.error(f"Excel export failed: {e}")

    async def _export_to_json(self, dashboard: Dict[str, Any]):
        """Export dashboard to JSON format"""
        try:
            import json

            export_path = Path(self.config.data_paths.export_path)
            export_path.mkdir(exist_ok=True)

            filename = f"performance_dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            filepath = export_path / filename

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(dashboard, f, indent=2, default=str)

            logger.info(f"📄 JSON report exported: {filepath}")

        except Exception as e:
            logger.error(f"JSON export failed: {e}")

    async def _send_notifications(self, dashboard: Dict[str, Any]):
        """Send notifications based on dashboard alerts"""
        try:
            alerts = dashboard.get("alerts", [])

            if alerts and self.toaster:
                # Send only the most critical alert to avoid spam
                critical_alert = alerts[0] if alerts else None

                if critical_alert:
                    self.toaster.show_toast(
                        "Performance Analysis Alert",
                        critical_alert,
                        duration=10
                    )
                    logger.info(f"📢 Notification sent: {critical_alert}")

        except Exception as e:
            logger.error(f"Failed to send notifications: {e}")

    async def _update_performance_stats(self, duration: float):
        """Update agent performance statistics"""
        self.analysis_stats["total_analyses"] += 1
        self.analysis_stats["last_analysis_time"] = datetime.now().isoformat()

        # Update average duration with exponential moving average
        if self.analysis_stats["avg_analysis_duration"] == 0:
            self.analysis_stats["avg_analysis_duration"] = duration
        else:
            alpha = 0.1
            self.analysis_stats["avg_analysis_duration"] = (
                alpha * duration + (1 - alpha) * self.analysis_stats["avg_analysis_duration"]
            )

    async def query_natural_language(self, query: str) -> str:
        """Process natural language queries about performance"""
        try:
            # Load recent trade data for querying
            trades_df = await self._load_trade_data()
            if trades_df is None or trades_df.is_empty():
                return "No trade data available to answer your query."

            # Evaluate trades for querying
            historical_path = Path(self.config.data_paths.historical_data)
            greeks_path = Path(self.config.data_paths.greeks_data)

            evaluated_trades = await self._trade_evaluator.evaluate_trade_level_performance(
                trades_df, historical_path, greeks_path
            )

            # Process query using natural language interface
            # This would be implemented in a separate NL query module
            return f"Query processed: {query} (Natural language processing not fully implemented yet)"

        except Exception as e:
            logger.error(f"Error processing natural language query: {e}")
            return f"Sorry, I encountered an error processing your query: {str(e)}"

    async def cleanup(self):
        """Cleanup resources and stop the agent"""
        try:
            logger.info("[CLEANUP] Cleaning up Refactored Performance Analysis Agent...")
            self.is_running = False

            # Stop heartbeat
            if hasattr(self, 'heartbeat') and self.heartbeat:
                self.heartbeat.stop_heartbeat()

            # Log final statistics
            logger.info(f"📊 Final Statistics:")
            logger.info(f"   Total Analyses: {self.analysis_stats['total_analyses']}")
            logger.info(f"   Avg Duration: {self.analysis_stats['avg_analysis_duration']:.2f}s")

            logger.info("[SUCCESS] Refactored Performance Analysis Agent cleaned up")

        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")

# Example usage and main function
async def main():
    """Main function for testing the refactored agent"""
    agent = RefactoredOptionsPerformanceAnalysisAgent()
    try:
        await agent.initialize()
        await agent.start()

        # Example natural language query
        response = await agent.query_natural_language("What is my win rate?")
        print(f"Query Response: {response}")

    except KeyboardInterrupt:
        logger.info("Agent interrupted by user. 🛑")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    asyncio.run(main())
