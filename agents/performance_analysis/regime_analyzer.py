#!/usr/bin/env python3
"""
Regime Analysis Module for Options Performance Analysis Agent

This module handles:
- Market regime-based performance evaluation
- Volatility regime analysis
- Time-of-day performance patterns
- Expiry proximity behavior analysis
- News day impact assessment
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import polars as pl

# Lazy imports for performance
def lazy_import(name):
    import importlib.util
    spec = importlib.util.find_spec(name)
    if spec is None:
        return None
    loader = importlib.util.LazyLoader(spec.loader)
    spec.loader = loader
    module = importlib.util.module_from_spec(spec)
    return module

np = lazy_import("numpy")

logger = logging.getLogger(__name__)

class RegimeAnalyzer:
    """Regime-based performance analysis engine"""
    
    def __init__(self, config=None):
        self.config = config
        self.regime_categories = {
            'market_regime': ['bullish', 'bearish', 'sideways', 'volatile'],
            'volatility_regime': ['low_vol', 'medium_vol', 'high_vol', 'extreme_vol'],
            'time_regime': ['morning', 'midday', 'afternoon', 'closing'],
            'expiry_regime': ['far', 'medium', 'near', 'expiry_day']
        }
    
    async def evaluate_regime_performance(self, evaluated_trades_df: pl.DataFrame) -> Dict[str, Any]:
        """
        Groups trade outcomes by market regime, volatility regime, time of day, expiry proximity, news day.
        Finds which strategy excels in what regime.
        """
        if evaluated_trades_df.is_empty():
            logger.warning("No evaluated trades to analyze for regime performance.")
            return {}

        logger.info("[ANALYZE] Evaluating regime-based strategy performance...")

        # Ensure required columns and add time-based features
        evaluated_trades_df = await self._prepare_regime_data(evaluated_trades_df)
        
        # Analyze performance by different regime types
        regime_insights = {}
        
        # Market regime analysis
        regime_insights["market_regime"] = await self._analyze_market_regime_performance(evaluated_trades_df)
        
        # Volatility regime analysis
        regime_insights["volatility_regime"] = await self._analyze_volatility_regime_performance(evaluated_trades_df)
        
        # Time-based analysis
        regime_insights["time_regime"] = await self._analyze_time_regime_performance(evaluated_trades_df)
        
        # Expiry proximity analysis
        regime_insights["expiry_regime"] = await self._analyze_expiry_regime_performance(evaluated_trades_df)
        
        # News day analysis
        regime_insights["news_impact"] = await self._analyze_news_day_performance(evaluated_trades_df)
        
        # Cross-regime analysis
        regime_insights["cross_regime"] = await self._analyze_cross_regime_performance(evaluated_trades_df)
        
        # Generate regime-based recommendations
        regime_insights["recommendations"] = await self._generate_regime_recommendations(regime_insights)
        
        logger.info("Regime-based strategy evaluation complete.")
        return regime_insights
    
    async def _prepare_regime_data(self, df: pl.DataFrame) -> pl.DataFrame:
        """Prepare data with regime-related features"""
        # Ensure 'entry_time' is datetime and extract time features
        df = df.with_columns([
            pl.col("entry_time").cast(pl.Datetime),
            pl.col("entry_time").dt.hour().alias("hour_of_day"),
            pl.col("entry_time").dt.weekday().alias("weekday"),
            pl.col("entry_time").dt.date().alias("trade_date")
        ])
        
        # Add time regime classification
        df = df.with_columns([
            pl.when(pl.col("hour_of_day").is_between(9, 11))
            .then(pl.lit("morning"))
            .when(pl.col("hour_of_day").is_between(11, 13))
            .then(pl.lit("midday"))
            .when(pl.col("hour_of_day").is_between(13, 15))
            .then(pl.lit("afternoon"))
            .otherwise(pl.lit("closing"))
            .alias("time_regime")
        ])
        
        return df
    
    async def _analyze_market_regime_performance(self, df: pl.DataFrame) -> Dict[str, Any]:
        """Analyze performance by market regime"""
        if "market_regime" not in df.columns:
            return {"error": "Market regime data not available"}
        
        regime_performance = df.group_by(["market_regime", "strategy_id"]).agg([
            pl.col("absolute_pnl").mean().alias("avg_pnl"),
            pl.col("roi_percent").mean().alias("avg_roi"),
            (pl.col("pnl_category") == "Profit").mean().alias("win_rate"),
            pl.len().alias("trade_count"),
            pl.col("absolute_pnl").sum().alias("total_pnl"),
            pl.col("execution_score").mean().alias("avg_execution_score")
        ]).sort(["market_regime", "avg_pnl"], descending=[False, True])
        
        # Find best and worst performers per regime
        regime_insights = {}
        for regime in self.regime_categories['market_regime']:
            regime_data = regime_performance.filter(pl.col("market_regime") == regime)
            
            if not regime_data.is_empty():
                best_strategy = regime_data.head(1)
                worst_strategy = regime_data.tail(1)
                
                regime_insights[regime] = {
                    "best_strategy": best_strategy.to_dicts()[0] if not best_strategy.is_empty() else None,
                    "worst_strategy": worst_strategy.to_dicts()[0] if not worst_strategy.is_empty() else None,
                    "total_trades": regime_data.select(pl.col("trade_count").sum()).item(),
                    "avg_performance": regime_data.select(pl.col("avg_pnl").mean()).item()
                }
        
        return regime_insights
    
    async def _analyze_volatility_regime_performance(self, df: pl.DataFrame) -> Dict[str, Any]:
        """Analyze performance by volatility regime"""
        if "volatility_regime" not in df.columns:
            return {"error": "Volatility regime data not available"}
        
        vol_performance = df.group_by(["volatility_regime", "strategy_id"]).agg([
            pl.col("absolute_pnl").mean().alias("avg_pnl"),
            pl.col("roi_percent").mean().alias("avg_roi"),
            (pl.col("pnl_category") == "Profit").mean().alias("win_rate"),
            pl.len().alias("trade_count"),
            pl.col("vega_pnl").mean().alias("avg_vega_pnl") if "vega_pnl" in df.columns else pl.lit(0).alias("avg_vega_pnl")
        ]).sort(["volatility_regime", "avg_pnl"], descending=[False, True])
        
        vol_insights = {}
        for vol_regime in self.regime_categories['volatility_regime']:
            vol_data = vol_performance.filter(pl.col("volatility_regime") == vol_regime)
            
            if not vol_data.is_empty():
                vol_insights[vol_regime] = {
                    "performance_summary": vol_data.to_dicts(),
                    "vega_impact": vol_data.select(pl.col("avg_vega_pnl").mean()).item(),
                    "total_trades": vol_data.select(pl.col("trade_count").sum()).item()
                }
        
        return vol_insights
    
    async def _analyze_time_regime_performance(self, df: pl.DataFrame) -> Dict[str, Any]:
        """Analyze performance by time of day"""
        time_performance = df.group_by(["time_regime", "strategy_id"]).agg([
            pl.col("absolute_pnl").mean().alias("avg_pnl"),
            pl.col("roi_percent").mean().alias("avg_roi"),
            (pl.col("pnl_category") == "Profit").mean().alias("win_rate"),
            pl.len().alias("trade_count"),
            pl.col("holding_time_minutes").mean().alias("avg_holding_time")
        ]).sort(["time_regime", "avg_pnl"], descending=[False, True])
        
        # Hourly performance analysis
        hourly_performance = df.group_by("hour_of_day").agg([
            pl.col("absolute_pnl").mean().alias("avg_pnl"),
            (pl.col("pnl_category") == "Profit").mean().alias("win_rate"),
            pl.len().alias("trade_count")
        ]).sort("hour_of_day")
        
        time_insights = {
            "time_regime_performance": time_performance.to_dicts(),
            "hourly_performance": hourly_performance.to_dicts(),
            "best_trading_hours": hourly_performance.sort("avg_pnl", descending=True).head(3).to_dicts(),
            "worst_trading_hours": hourly_performance.sort("avg_pnl").head(3).to_dicts()
        }
        
        return time_insights
    
    async def _analyze_expiry_regime_performance(self, df: pl.DataFrame) -> Dict[str, Any]:
        """Analyze performance by expiry proximity"""
        if "expiry_proximity" not in df.columns:
            return {"error": "Expiry proximity data not available"}
        
        expiry_performance = df.group_by(["expiry_proximity", "strategy_id"]).agg([
            pl.col("absolute_pnl").mean().alias("avg_pnl"),
            pl.col("roi_percent").mean().alias("avg_roi"),
            (pl.col("pnl_category") == "Profit").mean().alias("win_rate"),
            pl.len().alias("trade_count"),
            pl.col("theta_pnl").mean().alias("avg_theta_pnl") if "theta_pnl" in df.columns else pl.lit(0).alias("avg_theta_pnl")
        ]).sort(["expiry_proximity", "avg_pnl"], descending=[False, True])
        
        expiry_insights = {
            "expiry_performance": expiry_performance.to_dicts(),
            "theta_impact_by_expiry": expiry_performance.group_by("expiry_proximity").agg([
                pl.col("avg_theta_pnl").mean().alias("avg_theta_impact")
            ]).to_dicts()
        }
        
        return expiry_insights
    
    async def _analyze_news_day_performance(self, df: pl.DataFrame) -> Dict[str, Any]:
        """Analyze performance on news days vs regular days"""
        if "news_day" not in df.columns:
            return {"error": "News day data not available"}
        
        news_performance = df.group_by(["news_day", "strategy_id"]).agg([
            pl.col("absolute_pnl").mean().alias("avg_pnl"),
            pl.col("roi_percent").mean().alias("avg_roi"),
            (pl.col("pnl_category") == "Profit").mean().alias("win_rate"),
            pl.len().alias("trade_count"),
            pl.col("execution_score").mean().alias("avg_execution_score")
        ])
        
        news_insights = {
            "news_vs_regular": news_performance.to_dicts(),
            "news_day_impact": {}
        }
        
        # Calculate impact of news days
        news_day_data = news_performance.filter(pl.col("news_day") == True)
        regular_day_data = news_performance.filter(pl.col("news_day") == False)
        
        if not news_day_data.is_empty() and not regular_day_data.is_empty():
            news_avg_pnl = news_day_data.select(pl.col("avg_pnl").mean()).item()
            regular_avg_pnl = regular_day_data.select(pl.col("avg_pnl").mean()).item()
            
            news_insights["news_day_impact"] = {
                "pnl_difference": news_avg_pnl - regular_avg_pnl,
                "performance_ratio": news_avg_pnl / regular_avg_pnl if regular_avg_pnl != 0 else 0
            }
        
        return news_insights

    async def _analyze_cross_regime_performance(self, df: pl.DataFrame) -> Dict[str, Any]:
        """Analyze performance across multiple regime combinations"""
        cross_regime_insights = {}

        try:
            # Market regime + Volatility regime combination
            if "market_regime" in df.columns and "volatility_regime" in df.columns:
                market_vol_performance = df.group_by(["market_regime", "volatility_regime", "strategy_id"]).agg([
                    pl.col("absolute_pnl").mean().alias("avg_pnl"),
                    (pl.col("pnl_category") == "Profit").mean().alias("win_rate"),
                    pl.len().alias("trade_count")
                ]).filter(pl.col("trade_count") >= 5)  # Only include combinations with sufficient data

                cross_regime_insights["market_volatility"] = market_vol_performance.to_dicts()

            # Time regime + Market regime combination
            if "market_regime" in df.columns:
                time_market_performance = df.group_by(["time_regime", "market_regime", "strategy_id"]).agg([
                    pl.col("absolute_pnl").mean().alias("avg_pnl"),
                    (pl.col("pnl_category") == "Profit").mean().alias("win_rate"),
                    pl.len().alias("trade_count")
                ]).filter(pl.col("trade_count") >= 3)

                cross_regime_insights["time_market"] = time_market_performance.to_dicts()

            # Find best regime combinations for each strategy
            best_combinations = {}
            if "market_volatility" in cross_regime_insights:
                for strategy_data in cross_regime_insights["market_volatility"]:
                    strategy_id = strategy_data["strategy_id"]
                    if strategy_id not in best_combinations:
                        best_combinations[strategy_id] = []

                    if strategy_data["avg_pnl"] > 0 and strategy_data["win_rate"] > 0.6:
                        best_combinations[strategy_id].append({
                            "regime_combination": f"{strategy_data['market_regime']}_{strategy_data['volatility_regime']}",
                            "avg_pnl": strategy_data["avg_pnl"],
                            "win_rate": strategy_data["win_rate"]
                        })

            cross_regime_insights["best_combinations"] = best_combinations

        except Exception as e:
            logger.error(f"Error in cross-regime analysis: {e}")
            cross_regime_insights["error"] = str(e)

        return cross_regime_insights

    async def _generate_regime_recommendations(self, regime_insights: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations based on regime analysis"""
        recommendations = []

        try:
            # Market regime recommendations
            if "market_regime" in regime_insights:
                market_data = regime_insights["market_regime"]
                for regime, data in market_data.items():
                    if isinstance(data, dict) and "best_strategy" in data and data["best_strategy"]:
                        best_strategy = data["best_strategy"]["strategy_id"]
                        avg_pnl = data["best_strategy"]["avg_pnl"]
                        if avg_pnl > 100:  # Significant positive performance
                            recommendations.append(
                                f"🏆 In {regime} markets, prioritize {best_strategy} "
                                f"(avg P&L: ₹{avg_pnl:.0f})"
                            )

            # Time-based recommendations
            if "time_regime" in regime_insights:
                time_data = regime_insights["time_regime"]
                if "best_trading_hours" in time_data:
                    best_hours = time_data["best_trading_hours"][:2]  # Top 2 hours
                    if best_hours:
                        best_hour_list = [f"{hour['hour_of_day']}:00" for hour in best_hours]
                        recommendations.append(
                            f"⏰ Optimal trading hours: {', '.join(best_hour_list)} "
                            f"(avg P&L: ₹{best_hours[0]['avg_pnl']:.0f})"
                        )

            # Volatility regime recommendations
            if "volatility_regime" in regime_insights:
                vol_data = regime_insights["volatility_regime"]
                high_vol_performance = vol_data.get("high_vol", {})
                if high_vol_performance and "performance_summary" in high_vol_performance:
                    high_vol_strategies = high_vol_performance["performance_summary"]
                    if high_vol_strategies:
                        best_high_vol = max(high_vol_strategies, key=lambda x: x["avg_pnl"])
                        if best_high_vol["avg_pnl"] > 50:
                            recommendations.append(
                                f"🌊 In high volatility: Use {best_high_vol['strategy_id']} "
                                f"(avg P&L: ₹{best_high_vol['avg_pnl']:.0f})"
                            )

            # News day recommendations
            if "news_impact" in regime_insights:
                news_data = regime_insights["news_impact"]
                if "news_day_impact" in news_data:
                    impact = news_data["news_day_impact"]
                    pnl_diff = impact.get("pnl_difference", 0)
                    if abs(pnl_diff) > 20:
                        if pnl_diff > 0:
                            recommendations.append(
                                f"📰 News days show +₹{pnl_diff:.0f} better performance. "
                                "Consider increasing position sizes on news days."
                            )
                        else:
                            recommendations.append(
                                f"📰 News days show ₹{abs(pnl_diff):.0f} worse performance. "
                                "Consider reducing exposure or avoiding trades on news days."
                            )

            # Cross-regime recommendations
            if "cross_regime" in regime_insights:
                cross_data = regime_insights["cross_regime"]
                if "best_combinations" in cross_data:
                    for strategy_id, combinations in cross_data["best_combinations"].items():
                        if combinations:
                            best_combo = max(combinations, key=lambda x: x["avg_pnl"])
                            recommendations.append(
                                f"🎯 {strategy_id}: Best in {best_combo['regime_combination']} "
                                f"(₹{best_combo['avg_pnl']:.0f}, {best_combo['win_rate']:.1%} win rate)"
                            )

            # General recommendations
            if not recommendations:
                recommendations.append("📊 Insufficient regime data for specific recommendations. Continue collecting data.")

        except Exception as e:
            logger.error(f"Error generating regime recommendations: {e}")
            recommendations.append(f"❌ Error generating recommendations: {str(e)}")

        return recommendations

    async def get_regime_summary(self, regime_insights: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a comprehensive regime analysis summary"""
        summary = {
            "timestamp": datetime.now().isoformat(),
            "regime_coverage": {},
            "key_findings": [],
            "performance_highlights": {}
        }

        try:
            # Calculate regime coverage
            for regime_type, insights in regime_insights.items():
                if regime_type == "recommendations":
                    continue

                if isinstance(insights, dict) and not insights.get("error"):
                    summary["regime_coverage"][regime_type] = "Available"

                    # Extract key findings
                    if regime_type == "time_regime" and "best_trading_hours" in insights:
                        best_hour = insights["best_trading_hours"][0] if insights["best_trading_hours"] else None
                        if best_hour:
                            summary["key_findings"].append(
                                f"Best trading hour: {best_hour['hour_of_day']}:00 "
                                f"(₹{best_hour['avg_pnl']:.0f} avg P&L)"
                            )

                    elif regime_type == "market_regime":
                        best_regime = None
                        best_performance = float('-inf')
                        for regime, data in insights.items():
                            if isinstance(data, dict) and "avg_performance" in data:
                                if data["avg_performance"] > best_performance:
                                    best_performance = data["avg_performance"]
                                    best_regime = regime

                        if best_regime:
                            summary["key_findings"].append(
                                f"Best market regime: {best_regime} "
                                f"(₹{best_performance:.0f} avg P&L)"
                            )
                else:
                    summary["regime_coverage"][regime_type] = "Not Available"

            # Performance highlights
            summary["performance_highlights"] = {
                "total_regimes_analyzed": len([k for k, v in summary["regime_coverage"].items() if v == "Available"]),
                "recommendations_count": len(regime_insights.get("recommendations", [])),
                "analysis_completeness": len([k for k, v in summary["regime_coverage"].items() if v == "Available"]) / len(summary["regime_coverage"]) * 100
            }

        except Exception as e:
            logger.error(f"Error generating regime summary: {e}")
            summary["error"] = str(e)

        return summary
