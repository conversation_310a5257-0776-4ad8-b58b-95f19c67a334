#!/usr/bin/env python3
"""
Risk Management Feedback Module for Options Performance Analysis Agent

This module handles:
- Capital at risk monitoring and analysis
- Daily drawdown threshold evaluation
- Trading pause effectiveness analysis
- Risk control feedback generation
- Risk management recommendations
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import polars as pl

# Lazy imports for performance
def lazy_import(name):
    import importlib.util
    spec = importlib.util.find_spec(name)
    if spec is None:
        return None
    loader = importlib.util.LazyLoader(spec.loader)
    spec.loader = loader
    module = importlib.util.module_from_spec(spec)
    return module

np = lazy_import("numpy")

logger = logging.getLogger(__name__)

class RiskFeedbackAnalyzer:
    """Risk management feedback and analysis engine"""
    
    def __init__(self, config=None):
        self.config = config
        self.risk_thresholds = {
            'capital_at_risk_breach': 1.1,    # 110% of allowed capital
            'daily_drawdown_warning': 0.8,    # 80% of daily threshold
            'consecutive_losses': 3,           # 3 consecutive losses
            'win_rate_decline': 0.15          # 15% decline in win rate
        }
    
    async def provide_risk_control_feedback(self, evaluated_trades_df: pl.DataFrame) -> Dict[str, Any]:
        """
        Compares actual loss vs allowed capital at risk and daily drawdown vs threshold.
        Evaluates if trading was paused at the right time and if risky signals were filtered.
        Sends reports to Risk Management Agent (simulated).
        """
        if evaluated_trades_df.is_empty():
            logger.warning("No evaluated trades for risk control feedback.")
            return {}

        logger.info("[ANALYZE] Providing risk control feedback...")

        feedback = {
            "timestamp": datetime.now().isoformat(),
            "risk_analysis": {},
            "trading_pause_analysis": {},
            "capital_risk_analysis": {},
            "signal_filtering_analysis": {},
            "recommendations": [],
            "alerts": []
        }

        # Analyze capital at risk
        feedback["capital_risk_analysis"] = await self._analyze_capital_at_risk(evaluated_trades_df)
        
        # Analyze daily drawdown
        feedback["drawdown_analysis"] = await self._analyze_daily_drawdown(evaluated_trades_df)
        
        # Analyze trading pause effectiveness
        feedback["trading_pause_analysis"] = await self._analyze_trading_pause_effectiveness(evaluated_trades_df)
        
        # Analyze signal filtering effectiveness
        feedback["signal_filtering_analysis"] = await self._analyze_signal_filtering(evaluated_trades_df)
        
        # Generate risk management recommendations
        feedback["recommendations"] = await self._generate_risk_recommendations(feedback)
        
        # Generate risk alerts
        feedback["alerts"] = await self._generate_risk_alerts(feedback)
        
        # Calculate overall risk score
        feedback["risk_score"] = await self._calculate_risk_score(feedback)
        
        logger.info("Risk control feedback analysis complete.")
        return feedback
    
    async def _analyze_capital_at_risk(self, df: pl.DataFrame) -> Dict[str, Any]:
        """Analyze capital at risk vs actual losses"""
        capital_analysis = {}
        
        try:
            # Check for capital at risk breaches
            breaches = df.filter(
                pl.col("actual_loss").abs() > pl.col("allowed_capital_at_risk") * self.risk_thresholds['capital_at_risk_breach']
            )
            
            capital_analysis["total_trades"] = df.height
            capital_analysis["breach_count"] = breaches.height
            capital_analysis["breach_rate"] = (breaches.height / df.height * 100) if df.height > 0 else 0
            
            if not breaches.is_empty():
                capital_analysis["breach_details"] = breaches.select([
                    "trade_id", "actual_loss", "allowed_capital_at_risk", "strategy_id", "entry_time"
                ]).to_dicts()
                
                # Calculate severity of breaches
                breach_severity = breaches.with_columns([
                    (pl.col("actual_loss").abs() / pl.col("allowed_capital_at_risk")).alias("breach_ratio")
                ])
                
                capital_analysis["max_breach_ratio"] = breach_severity.select(pl.col("breach_ratio").max()).item()
                capital_analysis["avg_breach_ratio"] = breach_severity.select(pl.col("breach_ratio").mean()).item()
            else:
                capital_analysis["breach_details"] = []
                capital_analysis["max_breach_ratio"] = 0
                capital_analysis["avg_breach_ratio"] = 0
            
            # Analyze capital utilization efficiency
            capital_utilization = df.select([
                (pl.col("actual_loss").abs() / pl.col("allowed_capital_at_risk")).mean().alias("avg_utilization"),
                pl.col("allowed_capital_at_risk").sum().alias("total_capital_allocated"),
                pl.col("actual_loss").abs().sum().alias("total_capital_used")
            ]).row(0, named=True)
            
            capital_analysis["utilization_metrics"] = capital_utilization
            
        except Exception as e:
            logger.error(f"Error analyzing capital at risk: {e}")
            capital_analysis["error"] = str(e)
        
        return capital_analysis
    
    async def _analyze_daily_drawdown(self, df: pl.DataFrame) -> Dict[str, Any]:
        """Analyze daily drawdown vs thresholds"""
        drawdown_analysis = {}
        
        try:
            # Group by trading date
            daily_performance = df.group_by(pl.col("entry_time").dt.date().alias("trade_date")).agg([
                pl.col("absolute_pnl").sum().alias("daily_pnl"),
                pl.col("daily_drawdown_threshold").first().alias("daily_dd_threshold"),
                pl.col("trading_paused").any().alias("was_trading_paused"),
                pl.len().alias("daily_trade_count")
            ]).sort("trade_date")
            
            # Identify drawdown threshold breaches
            dd_breaches = daily_performance.filter(
                pl.col("daily_pnl") < -pl.col("daily_dd_threshold")
            )
            
            drawdown_analysis["total_trading_days"] = daily_performance.height
            drawdown_analysis["drawdown_breach_days"] = dd_breaches.height
            drawdown_analysis["breach_rate"] = (dd_breaches.height / daily_performance.height * 100) if daily_performance.height > 0 else 0
            
            if not dd_breaches.is_empty():
                drawdown_analysis["breach_details"] = dd_breaches.to_dicts()
                
                # Calculate severity of drawdown breaches
                breach_severity = dd_breaches.with_columns([
                    (pl.col("daily_pnl").abs() / pl.col("daily_dd_threshold")).alias("breach_severity")
                ])
                
                drawdown_analysis["max_breach_severity"] = breach_severity.select(pl.col("breach_severity").max()).item()
                drawdown_analysis["avg_breach_severity"] = breach_severity.select(pl.col("breach_severity").mean()).item()
            else:
                drawdown_analysis["breach_details"] = []
                drawdown_analysis["max_breach_severity"] = 0
                drawdown_analysis["avg_breach_severity"] = 0
            
            # Analyze daily P&L distribution
            daily_stats = daily_performance.select([
                pl.col("daily_pnl").mean().alias("avg_daily_pnl"),
                pl.col("daily_pnl").std().alias("daily_pnl_volatility"),
                pl.col("daily_pnl").min().alias("worst_daily_pnl"),
                pl.col("daily_pnl").max().alias("best_daily_pnl")
            ]).row(0, named=True)
            
            drawdown_analysis["daily_performance_stats"] = daily_stats
            
        except Exception as e:
            logger.error(f"Error analyzing daily drawdown: {e}")
            drawdown_analysis["error"] = str(e)
        
        return drawdown_analysis
    
    async def _analyze_trading_pause_effectiveness(self, df: pl.DataFrame) -> Dict[str, Any]:
        """Analyze effectiveness of trading pause decisions"""
        pause_analysis = {}
        
        try:
            # Group by date to analyze pause decisions
            daily_data = df.group_by(pl.col("entry_time").dt.date().alias("trade_date")).agg([
                pl.col("absolute_pnl").sum().alias("daily_pnl"),
                pl.col("trading_paused").any().alias("was_trading_paused"),
                pl.len().alias("trade_count"),
                pl.col("daily_drawdown_threshold").first().alias("dd_threshold")
            ])
            
            # Analyze pause decisions
            pause_evaluations = []
            correct_pauses = 0
            incorrect_pauses = 0
            missed_pauses = 0
            
            for row in daily_data.iter_rows(named=True):
                daily_pnl = row["daily_pnl"]
                was_paused = row["was_trading_paused"]
                dd_threshold = row["dd_threshold"]
                trade_date = row["trade_date"]
                
                if daily_pnl < -dd_threshold and was_paused:
                    # Correct pause - trading was paused on a bad day
                    pause_evaluations.append({
                        "date": str(trade_date),
                        "decision": "Correct Pause",
                        "daily_pnl": daily_pnl,
                        "evaluation": "✅ Trading paused appropriately during losses"
                    })
                    correct_pauses += 1
                    
                elif daily_pnl < -dd_threshold and not was_paused:
                    # Missed pause - should have paused but didn't
                    pause_evaluations.append({
                        "date": str(trade_date),
                        "decision": "Missed Pause",
                        "daily_pnl": daily_pnl,
                        "evaluation": "❌ Should have paused trading during significant losses"
                    })
                    missed_pauses += 1
                    
                elif daily_pnl >= 0 and was_paused:
                    # Potentially premature pause
                    pause_evaluations.append({
                        "date": str(trade_date),
                        "decision": "Premature Pause",
                        "daily_pnl": daily_pnl,
                        "evaluation": "⚠️ Trading paused on a profitable day - may be premature"
                    })
                    incorrect_pauses += 1
            
            pause_analysis["pause_evaluations"] = pause_evaluations
            pause_analysis["pause_effectiveness"] = {
                "correct_pauses": correct_pauses,
                "incorrect_pauses": incorrect_pauses,
                "missed_pauses": missed_pauses,
                "total_evaluations": len(pause_evaluations),
                "effectiveness_score": (correct_pauses / len(pause_evaluations) * 100) if pause_evaluations else 0
            }
            
        except Exception as e:
            logger.error(f"Error analyzing trading pause effectiveness: {e}")
            pause_analysis["error"] = str(e)
        
        return pause_analysis

    async def _analyze_signal_filtering(self, df: pl.DataFrame) -> Dict[str, Any]:
        """Analyze effectiveness of risky signal filtering"""
        filtering_analysis = {}

        try:
            # Analyze signal filtering effectiveness
            if "risky_signals_filtered" in df.columns:
                filtered_trades = df.filter(pl.col("risky_signals_filtered") == True)
                unfiltered_trades = df.filter(pl.col("risky_signals_filtered") == False)

                filtering_analysis["total_trades"] = df.height
                filtering_analysis["filtered_count"] = filtered_trades.height
                filtering_analysis["unfiltered_count"] = unfiltered_trades.height
                filtering_analysis["filtering_rate"] = (filtered_trades.height / df.height * 100) if df.height > 0 else 0

                # Compare performance of filtered vs unfiltered trades
                if not filtered_trades.is_empty() and not unfiltered_trades.is_empty():
                    filtered_performance = filtered_trades.select([
                        pl.col("absolute_pnl").mean().alias("avg_pnl"),
                        (pl.col("pnl_category") == "Profit").mean().alias("win_rate")
                    ]).row(0, named=True)

                    unfiltered_performance = unfiltered_trades.select([
                        pl.col("absolute_pnl").mean().alias("avg_pnl"),
                        (pl.col("pnl_category") == "Profit").mean().alias("win_rate")
                    ]).row(0, named=True)

                    filtering_analysis["performance_comparison"] = {
                        "filtered_performance": filtered_performance,
                        "unfiltered_performance": unfiltered_performance,
                        "filtering_effectiveness": filtered_performance["avg_pnl"] - unfiltered_performance["avg_pnl"]
                    }

                # Analyze signal confidence distribution
                if "signal_confidence" in df.columns:
                    confidence_analysis = df.group_by("risky_signals_filtered").agg([
                        pl.col("signal_confidence").mean().alias("avg_confidence"),
                        pl.col("signal_confidence").std().alias("confidence_std"),
                        pl.col("absolute_pnl").mean().alias("avg_pnl")
                    ])

                    filtering_analysis["confidence_analysis"] = confidence_analysis.to_dicts()
            else:
                filtering_analysis["error"] = "Risky signal filtering data not available"

        except Exception as e:
            logger.error(f"Error analyzing signal filtering: {e}")
            filtering_analysis["error"] = str(e)

        return filtering_analysis

    async def _generate_risk_recommendations(self, feedback: Dict[str, Any]) -> List[str]:
        """Generate risk management recommendations"""
        recommendations = []

        try:
            # Capital at risk recommendations
            capital_analysis = feedback.get("capital_risk_analysis", {})
            if capital_analysis.get("breach_rate", 0) > 5:  # More than 5% breach rate
                recommendations.append(
                    f"🚨 Capital at risk breach rate is {capital_analysis['breach_rate']:.1f}%. "
                    "Review position sizing and risk limits."
                )

            if capital_analysis.get("max_breach_ratio", 0) > 1.5:
                recommendations.append(
                    f"⚠️ Maximum capital breach ratio is {capital_analysis['max_breach_ratio']:.1f}x. "
                    "Implement stricter stop-loss mechanisms."
                )

            # Drawdown recommendations
            drawdown_analysis = feedback.get("drawdown_analysis", {})
            if drawdown_analysis.get("breach_rate", 0) > 10:  # More than 10% of days breach drawdown
                recommendations.append(
                    f"📉 Daily drawdown threshold breached on {drawdown_analysis['breach_rate']:.1f}% of trading days. "
                    "Consider reducing daily risk limits."
                )

            # Trading pause recommendations
            pause_analysis = feedback.get("trading_pause_analysis", {})
            pause_effectiveness = pause_analysis.get("pause_effectiveness", {})
            if pause_effectiveness.get("effectiveness_score", 0) < 70:
                recommendations.append(
                    f"⏸️ Trading pause effectiveness is {pause_effectiveness['effectiveness_score']:.1f}%. "
                    "Review pause trigger conditions and timing."
                )

            if pause_effectiveness.get("missed_pauses", 0) > 2:
                recommendations.append(
                    f"❌ {pause_effectiveness['missed_pauses']} instances where trading should have been paused. "
                    "Implement more aggressive pause triggers."
                )

            # Signal filtering recommendations
            filtering_analysis = feedback.get("signal_filtering_analysis", {})
            if "performance_comparison" in filtering_analysis:
                filtering_effectiveness = filtering_analysis["performance_comparison"]["filtering_effectiveness"]
                if filtering_effectiveness < 0:
                    recommendations.append(
                        f"🎯 Signal filtering may be too aggressive (₹{abs(filtering_effectiveness):.0f} opportunity cost). "
                        "Review filtering criteria."
                    )
                elif filtering_effectiveness > 50:
                    recommendations.append(
                        f"✅ Signal filtering is effective (+₹{filtering_effectiveness:.0f} improvement). "
                        "Consider expanding filtering criteria."
                    )

            # General risk management recommendations
            if not recommendations:
                recommendations.append("✅ Risk management controls appear to be functioning effectively.")

        except Exception as e:
            logger.error(f"Error generating risk recommendations: {e}")
            recommendations.append(f"❌ Error generating recommendations: {str(e)}")

        return recommendations

    async def _generate_risk_alerts(self, feedback: Dict[str, Any]) -> List[str]:
        """Generate immediate risk alerts"""
        alerts = []

        try:
            # Critical capital breach alerts
            capital_analysis = feedback.get("capital_risk_analysis", {})
            if capital_analysis.get("max_breach_ratio", 0) > 2.0:
                alerts.append(
                    f"🚨 CRITICAL: Capital breach ratio of {capital_analysis['max_breach_ratio']:.1f}x detected. "
                    "Immediate review required."
                )

            # Severe drawdown alerts
            drawdown_analysis = feedback.get("drawdown_analysis", {})
            if drawdown_analysis.get("max_breach_severity", 0) > 2.0:
                alerts.append(
                    f"🚨 SEVERE: Daily drawdown breach severity of {drawdown_analysis['max_breach_severity']:.1f}x. "
                    "Risk limits may be inadequate."
                )

            # Consecutive failure alerts
            pause_analysis = feedback.get("trading_pause_analysis", {})
            if pause_analysis.get("pause_effectiveness", {}).get("missed_pauses", 0) > 3:
                alerts.append(
                    "🚨 ALERT: Multiple missed trading pause opportunities. "
                    "Automated risk controls may be failing."
                )

        except Exception as e:
            logger.error(f"Error generating risk alerts: {e}")
            alerts.append(f"❌ Error generating alerts: {str(e)}")

        return alerts

    async def _calculate_risk_score(self, feedback: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall risk management score"""
        try:
            risk_components = {}

            # Capital management score (0-100)
            capital_analysis = feedback.get("capital_risk_analysis", {})
            breach_rate = capital_analysis.get("breach_rate", 0)
            capital_score = max(0, 100 - breach_rate * 10)  # Penalize breaches heavily
            risk_components["capital_management"] = capital_score

            # Drawdown control score (0-100)
            drawdown_analysis = feedback.get("drawdown_analysis", {})
            dd_breach_rate = drawdown_analysis.get("breach_rate", 0)
            drawdown_score = max(0, 100 - dd_breach_rate * 5)  # Moderate penalty for drawdown breaches
            risk_components["drawdown_control"] = drawdown_score

            # Trading pause effectiveness score (0-100)
            pause_analysis = feedback.get("trading_pause_analysis", {})
            pause_effectiveness = pause_analysis.get("pause_effectiveness", {}).get("effectiveness_score", 50)
            risk_components["pause_effectiveness"] = pause_effectiveness

            # Signal filtering score (0-100)
            filtering_analysis = feedback.get("signal_filtering_analysis", {})
            if "performance_comparison" in filtering_analysis:
                filtering_effectiveness = filtering_analysis["performance_comparison"]["filtering_effectiveness"]
                filtering_score = min(100, max(0, 50 + filtering_effectiveness))  # Centered around 50
            else:
                filtering_score = 50  # Neutral score if no data
            risk_components["signal_filtering"] = filtering_score

            # Calculate weighted overall score
            weights = {
                "capital_management": 0.4,
                "drawdown_control": 0.3,
                "pause_effectiveness": 0.2,
                "signal_filtering": 0.1
            }

            overall_score = sum(risk_components[component] * weights[component]
                              for component in risk_components)

            # Risk level classification
            if overall_score >= 80:
                risk_level = "Low"
            elif overall_score >= 60:
                risk_level = "Moderate"
            elif overall_score >= 40:
                risk_level = "High"
            else:
                risk_level = "Critical"

            return {
                "overall_risk_score": round(overall_score, 1),
                "risk_level": risk_level,
                "component_scores": {k: round(v, 1) for k, v in risk_components.items()},
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error calculating risk score: {e}")
            return {"error": str(e)}
