from datetime import datetime
from typing import Any
import polars as pl
from pydantic import BaseModel, Field
from dataclasses import dataclass

# Centralized Schema Definitions using Pydantic
class TradeDataSchema(BaseModel):
    trade_id: str
    strategy_id: str
    entry_time: datetime
    exit_time: datetime
    entry_price: float
    exit_price: float
    quantity: int
    trade_type: str = Field(..., pattern="^(CE|PE)$")
    expected_entry_price: float
    expected_exit_price: float
    signal_confidence: float
    is_target_hit: bool
    is_sl_hit: bool
    is_manual_exit: bool
    market_regime: str
    volatility_regime: str
    news_day: bool
    expiry_proximity: str
    model_predicted_direction: str = Field(..., pattern="^(long|short)$")
    model_predicted_roi: float
    actual_direction: str = Field(..., pattern="^(long|short)$")
    allowed_capital_at_risk: float
    actual_loss: float
    daily_drawdown_threshold: float
    trading_paused: bool
    risky_signals_filtered: bool
    signal_source: str
    strike_price: float
    option_type: str = Field(..., pattern="^(CALL|PUT)$")
    underlying_entry_price: float
    underlying_exit_price: float

class HistoricalDataSchema(BaseModel):
    timestamp: datetime
    close: float

class GreeksDataSchema(BaseModel):
    trade_id: str
    timestamp: datetime
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float
    implied_volatility: float
    time_to_expiry: float
    underlying_price: float
    strike_price: float
    option_price: float

@dataclass
class GreeksData:
    """Data class for Options Greeks"""
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float

@dataclass
class PerformanceMetrics:
    """Data class for comprehensive performance metrics"""
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    max_drawdown: float
    var_95: float
    cvar_95: float
    win_rate: float
    profit_factor: float

def pydantic_type_to_polars_type(pydantic_type: Any) -> pl.DataType:
    """Converts Pydantic types to Polars data types."""
    if pydantic_type is str:
        return pl.Utf8
    elif pydantic_type is int:
        return pl.Int64
    elif pydantic_type is float:
        return pl.Float64
    elif pydantic_type is bool:
        return pl.Boolean
    elif pydantic_type is datetime:
        return pl.Datetime
    else:
        # Default to Utf8 for unknown types or more complex Pydantic types
        return pl.Utf8
