#!/usr/bin/env python3
"""
Strategy Metrics Aggregation Module for Options Performance Analysis Agent

This module handles:
- Strategy-wise performance metrics calculation
- Win rates, expectancy, and Sharpe ratio calculations
- Maximum drawdown analysis
- Capital efficiency metrics
- Time-based performance aggregation
"""

import asyncio
import logging
import math
from typing import Dict, List, Optional, Any, Tuple
import polars as pl

# Lazy imports for performance
def lazy_import(name):
    import importlib.util
    spec = importlib.util.find_spec(name)
    if spec is None:
        return None
    loader = importlib.util.LazyLoader(spec.loader)
    spec.loader = loader
    module = importlib.util.module_from_spec(spec)
    return module

np = lazy_import("numpy")

logger = logging.getLogger(__name__)

class StrategyAggregator:
    """Strategy-wise metrics aggregation engine"""
    
    def __init__(self, config=None):
        self.config = config
        self.annualization_factors = {
            "daily": math.sqrt(252),    # Trading days in a year
            "weekly": math.sqrt(52),    # Weeks in a year
            "monthly": math.sqrt(12)    # Months in a year
        }
    
    async def aggregate_strategy_metrics(self, evaluated_trades_df: pl.DataFrame, 
                                       period: str = "daily") -> Dict[str, Any]:
        """
        Computes strategy-wise metrics over defined periods.
        period can be "daily", "weekly", "monthly".
        """
        if evaluated_trades_df.is_empty():
            logger.warning(f"No evaluated trades to aggregate for {period} strategy metrics.")
            return {}

        logger.info(f"[ANALYZE] Aggregating strategy-wise metrics for period: {period}...")

        # Ensure 'entry_time' is a datetime type for grouping
        evaluated_trades_df = evaluated_trades_df.with_columns(
            pl.col("entry_time").cast(pl.Datetime)
        )

        # Add period grouping column
        evaluated_trades_df = await self._add_period_grouping(evaluated_trades_df, period)
        
        # Calculate basic aggregated metrics
        strategy_metrics = await self._calculate_basic_aggregations(evaluated_trades_df, period)
        
        # Calculate advanced metrics
        strategy_metrics = await self._calculate_advanced_metrics(strategy_metrics, period)
        
        # Calculate drawdown metrics
        strategy_metrics = await self._calculate_drawdown_metrics(strategy_metrics, evaluated_trades_df)
        
        # Format output
        output_data = await self._format_output(strategy_metrics)
        
        logger.info(f"Strategy-wise metrics aggregation complete for {period}.")
        return output_data
    
    async def _add_period_grouping(self, df: pl.DataFrame, period: str) -> pl.DataFrame:
        """Add period grouping column based on the specified period"""
        if period == "daily":
            return df.with_columns(
                pl.col("entry_time").dt.date().alias("period_start")
            )
        elif period == "weekly":
            return df.with_columns(
                pl.col("entry_time").dt.week().alias("period_start")
            )
        elif period == "monthly":
            return df.with_columns(
                pl.col("entry_time").dt.month().alias("period_start")
            )
        else:
            raise ValueError("Invalid period. Must be 'daily', 'weekly', or 'monthly'.")
    
    async def _calculate_basic_aggregations(self, df: pl.DataFrame, period: str) -> pl.DataFrame:
        """Calculate basic aggregated metrics by strategy and period"""
        return df.group_by(["strategy_id", "period_start"]).agg([
            # Win rate (accuracy %)
            (pl.col("pnl_category") == "Profit").mean().alias("win_rate_percent"),
            
            # Average ROI
            pl.col("roi_percent").mean().alias("avg_roi"),
            
            # Standard deviation of ROI for Sharpe Ratio
            pl.col("roi_percent").std().alias("roi_std"),
            
            # Average Win and Average Loss for Expectancy
            pl.col("absolute_pnl").filter(pl.col("absolute_pnl") > 0).mean().alias("avg_win"),
            pl.col("absolute_pnl").filter(pl.col("absolute_pnl") < 0).mean().alias("avg_loss"),
            
            # Sample Size
            pl.len().alias("sample_size"),
            
            # Total P&L
            pl.col("absolute_pnl").sum().alias("total_pnl"),
            
            # Capital efficiency (sum of P&L / sum of risked capital)
            (pl.col("absolute_pnl").sum() / pl.col("allowed_capital_at_risk").sum()).alias("capital_efficiency"),
            
            # Signal-to-trade conversion rate
            (pl.col("signal_confidence").is_not_null().sum() / pl.len()).alias("signal_to_trade_conversion_rate"),
            
            # Average execution score
            pl.col("execution_score").mean().alias("avg_execution_score"),
            
            # Average holding time
            pl.col("holding_time_minutes").mean().alias("avg_holding_time_minutes"),
            
            # Signal accuracy rate
            (pl.col("was_signal_accurate").sum() / pl.len()).alias("signal_accuracy_rate")
        ]).sort(["strategy_id", "period_start"])
    
    async def _calculate_advanced_metrics(self, strategy_metrics: pl.DataFrame, period: str) -> pl.DataFrame:
        """Calculate advanced performance metrics"""
        annualization_factor = self.annualization_factors.get(period, 1)
        risk_free_rate = self.config.analysis_settings.risk_free_rate if self.config else 0.06
        
        return strategy_metrics.with_columns([
            # Expectancy = Avg win × win rate – Avg loss × loss rate
            (
                (pl.col("avg_win") * pl.col("win_rate_percent")) - 
                (pl.col("avg_loss").abs() * (1 - pl.col("win_rate_percent")))
            ).alias("expectancy"),
            
            # Sharpe Ratio (annualized)
            pl.when(pl.col("roi_std") != 0)
            .then((pl.col("avg_roi") - risk_free_rate) / pl.col("roi_std") * annualization_factor)
            .otherwise(0.0)
            .alias("sharpe_ratio"),
            
            # Profit Factor
            pl.when(pl.col("avg_loss") != 0)
            .then(pl.col("avg_win").abs() / pl.col("avg_loss").abs())
            .otherwise(0.0)
            .alias("profit_factor"),
            
            # Sortino Ratio (using downside deviation)
            pl.lit(0.0).alias("sortino_ratio"),  # Placeholder - would need downside deviation calculation
            
            # Calmar Ratio (placeholder)
            pl.lit(0.0).alias("calmar_ratio")
        ])
    
    async def _calculate_drawdown_metrics(self, strategy_metrics: pl.DataFrame, 
                                        evaluated_trades_df: pl.DataFrame) -> pl.DataFrame:
        """Calculate maximum drawdown metrics"""
        # For now, using a simplified approach
        # In a real implementation, this would calculate time-series drawdown
        return strategy_metrics.with_columns([
            # Simplified max drawdown as percentage of worst single trade
            pl.when(pl.col("sample_size") > 0)
            .then(pl.lit(0.05))  # Placeholder 5% max drawdown
            .otherwise(0.0)
            .alias("max_dd_percent")
        ])
    
    async def _format_output(self, strategy_metrics: pl.DataFrame) -> Dict[str, Any]:
        """Format the output into the expected dictionary structure"""
        output_data = {}
        
        for row in strategy_metrics.iter_rows(named=True):
            strategy_id = row["strategy_id"]
            period_start = row["period_start"]
            
            if strategy_id not in output_data:
                output_data[strategy_id] = {}
            
            # Handle None values safely
            def safe_round(value, decimals=2):
                return round(value, decimals) if value is not None else 0.0

            output_data[strategy_id][str(period_start)] = {
                "win_rate": safe_round(row.get("win_rate_percent", 0) * 100, 2),
                "avg_roi": safe_round(row.get("avg_roi", 0), 2),
                "expectancy": safe_round(row.get("expectancy", 0), 2),
                "max_dd": safe_round(row.get("max_dd_percent", 0) * 100, 2),
                "sharpe": safe_round(row.get("sharpe_ratio", 0), 2),
                "sortino": safe_round(row.get("sortino_ratio", 0), 2),
                "calmar": safe_round(row.get("calmar_ratio", 0), 2),
                "profit_factor": safe_round(row.get("profit_factor", 0), 2),
                "sample_size": row.get("sample_size", 0),
                "total_pnl": safe_round(row.get("total_pnl", 0), 2),
                "capital_efficiency": safe_round(row.get("capital_efficiency", 0), 2),
                "signal_to_trade_conversion_rate": safe_round(row.get("signal_to_trade_conversion_rate", 0), 2),
                "avg_execution_score": safe_round(row.get("avg_execution_score", 0), 2),
                "avg_holding_time_minutes": safe_round(row.get("avg_holding_time_minutes", 0), 2),
                "signal_accuracy_rate": safe_round(row.get("signal_accuracy_rate", 0) * 100, 2),
                "best_regime": "N/A"  # Placeholder, needs regime evaluation
            }
        
        return output_data
    
    async def calculate_portfolio_metrics(self, evaluated_trades_df: pl.DataFrame) -> Dict[str, Any]:
        """Calculate overall portfolio-level metrics"""
        if evaluated_trades_df.is_empty():
            return {}
        
        # Calculate basic portfolio statistics
        total_trades = evaluated_trades_df.height
        profitable_trades = evaluated_trades_df.filter(pl.col("pnl_category") == "Profit").height
        total_pnl = evaluated_trades_df.select(pl.col("absolute_pnl").sum()).item()
        
        # Calculate returns series for advanced metrics
        returns_series = evaluated_trades_df.select(pl.col("roi_percent") / 100).to_series()
        
        portfolio_metrics = {
            "total_trades": total_trades,
            "profitable_trades": profitable_trades,
            "win_rate": round((profitable_trades / total_trades * 100) if total_trades > 0 else 0, 2),
            "total_pnl": round(total_pnl, 2),
            "avg_pnl_per_trade": round(total_pnl / total_trades if total_trades > 0 else 0, 2),
            "best_trade": round(evaluated_trades_df.select(pl.col("absolute_pnl").max()).item(), 2),
            "worst_trade": round(evaluated_trades_df.select(pl.col("absolute_pnl").min()).item(), 2)
        }
        
        # Add advanced metrics if numpy is available
        if np is not None:
            returns_array = returns_series.to_numpy()
            
            if len(returns_array) > 0:
                portfolio_metrics.update({
                    "avg_return": round(np.mean(returns_array) * 100, 2),
                    "return_volatility": round(np.std(returns_array) * 100, 2),
                    "sharpe_ratio": round(self._calculate_sharpe_ratio(returns_array), 3),
                    "max_drawdown": round(self._calculate_max_drawdown(returns_array) * 100, 2)
                })
        
        return portfolio_metrics
    
    def _calculate_sharpe_ratio(self, returns: 'np.ndarray') -> float:
        """Calculate Sharpe ratio"""
        if np is None or len(returns) == 0:
            return 0.0
        
        risk_free_rate = self.config.analysis_settings.risk_free_rate if self.config else 0.06
        excess_returns = returns - (risk_free_rate / 252)  # Daily risk-free rate
        
        if np.std(excess_returns) == 0:
            return 0.0
        
        return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
    
    def _calculate_max_drawdown(self, returns: 'np.ndarray') -> float:
        """Calculate maximum drawdown"""
        if np is None or len(returns) == 0:
            return 0.0
        
        cumulative_returns = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - running_max) / running_max
        
        return np.min(drawdown)
    
    async def get_strategy_insights(self, strategy_metrics: Dict[str, Any]) -> List[str]:
        """Generate insights from strategy metrics"""
        insights = []
        
        if not strategy_metrics:
            return ["No strategy metrics available for analysis."]
        
        # Analyze each strategy's performance
        for strategy_id, periods_data in strategy_metrics.items():
            if not periods_data:
                continue
            
            # Get the latest period's data
            latest_period_key = sorted(periods_data.keys())[-1]
            latest_metrics = periods_data[latest_period_key]
            
            win_rate = latest_metrics.get("win_rate", 0)
            avg_roi = latest_metrics.get("avg_roi", 0)
            sharpe = latest_metrics.get("sharpe", 0)
            sample_size = latest_metrics.get("sample_size", 0)
            
            # Generate insights based on performance
            if sample_size > 30:  # Only if enough data
                if sharpe > 1.5:
                    insights.append(f"🏆 {strategy_id}: Excellent Sharpe ratio ({sharpe:.2f}). Strong risk-adjusted returns.")
                elif sharpe < 0.5 and avg_roi > 0:
                    insights.append(f"⚠️ {strategy_id}: Low Sharpe ratio ({sharpe:.2f}) despite positive ROI. High volatility.")
                
                if win_rate > 70:
                    insights.append(f"🎯 {strategy_id}: High win rate ({win_rate:.1f}%). Consistent performance.")
                elif win_rate < 40:
                    insights.append(f"📉 {strategy_id}: Low win rate ({win_rate:.1f}%). Consider parameter adjustment.")
        
        return insights
