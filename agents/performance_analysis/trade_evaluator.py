#!/usr/bin/env python3
"""
Trade Evaluation Module for Options Performance Analysis Agent

This module handles:
- Trade-level performance evaluation
- ROI calculations and analysis
- Slippage analysis and categorization
- Confidence vs outcome correlation
- Entry precision analysis
"""

import asyncio
import logging
import math
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import polars as pl

# Lazy imports for performance
def lazy_import(name):
    import importlib.util
    spec = importlib.util.find_spec(name)
    if spec is None:
        return None
    loader = importlib.util.LazyLoader(spec.loader)
    spec.loader = loader
    module = importlib.util.module_from_spec(spec)
    return module

np = lazy_import("numpy")

logger = logging.getLogger(__name__)

class TradeEvaluator:
    """Trade-level performance evaluation engine"""
    
    def __init__(self, config=None):
        self.config = config
        self.slippage_thresholds = {
            'minimal': 0.01,    # 1%
            'acceptable': 0.03, # 3%
            'significant': 0.05 # 5%
        }
    
    async def evaluate_trade_level_performance(self, trades_df: pl.DataFrame, 
                                             historical_data_path: Path, 
                                             greeks_data_path: Path) -> pl.DataFrame:
        """
        Analyzes each completed trade and adds performance metrics.
        Adds columns: pnl_category, was_signal_accurate, slippage_reason, execution_score, entry_precision
        """
        if trades_df.is_empty():
            logger.warning("No trades to evaluate.")
            return trades_df

        logger.info(f"[ANALYZE] Evaluating trade-level performance for {trades_df.height} trades...")

        # Calculate basic performance metrics
        evaluated_df = await self._calculate_basic_metrics(trades_df)
        
        # Add slippage analysis
        evaluated_df = await self._analyze_slippage(evaluated_df)
        
        # Add signal accuracy analysis
        evaluated_df = await self._analyze_signal_accuracy(evaluated_df)
        
        # Add execution scoring
        evaluated_df = await self._calculate_execution_score(evaluated_df)
        
        # Add entry precision analysis
        evaluated_df = await self._analyze_entry_precision(evaluated_df, historical_data_path)
        
        # Add Greeks-based analysis if available
        evaluated_df = await self._add_greeks_analysis(evaluated_df, greeks_data_path)
        
        logger.info("Trade-level performance evaluation complete.")
        return evaluated_df
    
    async def _calculate_basic_metrics(self, trades_df: pl.DataFrame) -> pl.DataFrame:
        """Calculate basic performance metrics for each trade"""
        return trades_df.with_columns([
            # ROI calculation
            ((pl.col("exit_price") - pl.col("entry_price")) / pl.col("entry_price") * 100).alias("roi_percent"),
            
            # Absolute P&L
            ((pl.col("exit_price") - pl.col("entry_price")) * pl.col("quantity")).alias("absolute_pnl"),
            
            # Holding time in minutes
            ((pl.col("exit_time") - pl.col("entry_time")).dt.total_minutes()).alias("holding_time_minutes"),
            
            # P&L category
            pl.when(pl.col("exit_price") > pl.col("entry_price"))
            .then(pl.lit("Profit"))
            .otherwise(pl.lit("Loss"))
            .alias("pnl_category"),
            
            # Entry slippage
            ((pl.col("entry_price") - pl.col("expected_entry_price")) / pl.col("expected_entry_price") * 100).alias("entry_slippage"),
            
            # Exit slippage
            ((pl.col("exit_price") - pl.col("expected_exit_price")) / pl.col("expected_exit_price") * 100).alias("exit_slippage")
        ])
    
    async def _analyze_slippage(self, trades_df: pl.DataFrame) -> pl.DataFrame:
        """Analyze and categorize slippage for each trade"""
        return trades_df.with_columns([
            # Total slippage impact
            (pl.col("entry_slippage").abs() + pl.col("exit_slippage").abs()).alias("total_slippage"),
            
            # Slippage reason categorization
            pl.when(pl.col("entry_slippage").abs() + pl.col("exit_slippage").abs() < self.slippage_thresholds['minimal'])
            .then(pl.lit("Minimal Slippage"))
            .when(pl.col("entry_slippage").abs() + pl.col("exit_slippage").abs() < self.slippage_thresholds['acceptable'])
            .then(pl.lit("Acceptable Slippage"))
            .when(pl.col("entry_slippage").abs() + pl.col("exit_slippage").abs() < self.slippage_thresholds['significant'])
            .then(pl.lit("Significant Slippage"))
            .otherwise(pl.lit("Excessive Slippage"))
            .alias("slippage_reason")
        ])
    
    async def _analyze_signal_accuracy(self, trades_df: pl.DataFrame) -> pl.DataFrame:
        """Analyze signal accuracy and confidence correlation"""
        return trades_df.with_columns([
            # Was signal accurate (predicted direction matches actual outcome)
            (pl.col("model_predicted_direction") == pl.col("actual_direction")).alias("was_signal_accurate"),
            
            # Confidence vs outcome correlation score
            pl.when((pl.col("signal_confidence") > 0.7) & (pl.col("pnl_category") == "Profit"))
            .then(pl.lit(1.0))  # High confidence, profitable
            .when((pl.col("signal_confidence") > 0.7) & (pl.col("pnl_category") == "Loss"))
            .then(pl.lit(-1.0))  # High confidence, loss
            .when((pl.col("signal_confidence") < 0.5) & (pl.col("pnl_category") == "Profit"))
            .then(pl.lit(0.5))  # Low confidence, profitable
            .when((pl.col("signal_confidence") < 0.5) & (pl.col("pnl_category") == "Loss"))
            .then(pl.lit(0.0))  # Low confidence, loss
            .otherwise(pl.lit(0.25))  # Medium confidence
            .alias("confidence_outcome_correlation")
        ])
    
    async def _calculate_execution_score(self, trades_df: pl.DataFrame) -> pl.DataFrame:
        """Calculate execution quality score based on multiple factors"""
        return trades_df.with_columns([
            # Execution score (0-100)
            pl.when(pl.col("slippage_reason") == "Minimal Slippage")
            .then(pl.lit(90))
            .when(pl.col("slippage_reason") == "Acceptable Slippage")
            .then(pl.lit(75))
            .when(pl.col("slippage_reason") == "Significant Slippage")
            .then(pl.lit(50))
            .otherwise(pl.lit(25))
            .alias("base_execution_score")
        ]).with_columns([
            # Adjust score based on signal accuracy
            pl.when(pl.col("was_signal_accurate"))
            .then(pl.col("base_execution_score") + 10)
            .otherwise(pl.col("base_execution_score") - 5)
            .alias("execution_score")
        ])
    
    async def _analyze_entry_precision(self, trades_df: pl.DataFrame, historical_data_path: Path) -> pl.DataFrame:
        """Analyze entry precision relative to local highs/lows"""
        try:
            # Load historical data if available
            if historical_data_path.exists():
                historical_df = await asyncio.to_thread(pl.read_parquet, historical_data_path)
                
                # For now, add a placeholder entry precision score
                # In a real implementation, this would compare entry price to local extremes
                return trades_df.with_columns([
                    pl.when(pl.col("trade_type") == "CE")
                    .then(pl.lit(0.75))  # Placeholder: 75% precision for CE trades
                    .when(pl.col("trade_type") == "PE")
                    .then(pl.lit(0.70))  # Placeholder: 70% precision for PE trades
                    .otherwise(pl.lit(0.65))
                    .alias("entry_precision")
                ])
            else:
                logger.warning(f"Historical data not found at {historical_data_path}")
                return trades_df.with_columns([
                    pl.lit(None).alias("entry_precision")
                ])
                
        except Exception as e:
            logger.error(f"Error analyzing entry precision: {e}")
            return trades_df.with_columns([
                pl.lit(None).alias("entry_precision")
            ])
    
    async def _add_greeks_analysis(self, trades_df: pl.DataFrame, greeks_data_path: Path) -> pl.DataFrame:
        """Add Greeks-based P&L attribution analysis"""
        try:
            if greeks_data_path.exists():
                greeks_df = await asyncio.to_thread(pl.read_parquet, greeks_data_path)
                
                # Join with Greeks data and calculate P&L attribution
                # This is a simplified implementation
                return trades_df.with_columns([
                    pl.lit(0.0).alias("delta_pnl"),
                    pl.lit(0.0).alias("gamma_pnl"),
                    pl.lit(0.0).alias("theta_pnl"),
                    pl.lit(0.0).alias("vega_pnl"),
                    pl.lit(0.0).alias("rho_pnl")
                ])
            else:
                logger.warning(f"Greeks data not found at {greeks_data_path}")
                return trades_df.with_columns([
                    pl.lit(None).alias("delta_pnl"),
                    pl.lit(None).alias("gamma_pnl"),
                    pl.lit(None).alias("theta_pnl"),
                    pl.lit(None).alias("vega_pnl"),
                    pl.lit(None).alias("rho_pnl")
                ])
                
        except Exception as e:
            logger.error(f"Error adding Greeks analysis: {e}")
            return trades_df.with_columns([
                pl.lit(None).alias("delta_pnl"),
                pl.lit(None).alias("gamma_pnl"),
                pl.lit(None).alias("theta_pnl"),
                pl.lit(None).alias("vega_pnl"),
                pl.lit(None).alias("rho_pnl")
            ])
    
    async def calculate_trade_statistics(self, evaluated_trades_df: pl.DataFrame) -> Dict[str, Any]:
        """Calculate comprehensive trade statistics"""
        if evaluated_trades_df.is_empty():
            return {}
        
        stats = {}
        
        # Basic statistics
        total_trades = evaluated_trades_df.height
        profitable_trades = evaluated_trades_df.filter(pl.col("pnl_category") == "Profit").height
        
        stats.update({
            "total_trades": total_trades,
            "profitable_trades": profitable_trades,
            "losing_trades": total_trades - profitable_trades,
            "win_rate": round((profitable_trades / total_trades * 100) if total_trades > 0 else 0, 2),
            "total_pnl": round(evaluated_trades_df.select(pl.col("absolute_pnl").sum()).item(), 2),
            "avg_pnl_per_trade": round(evaluated_trades_df.select(pl.col("absolute_pnl").mean()).item(), 2),
            "best_trade": round(evaluated_trades_df.select(pl.col("absolute_pnl").max()).item(), 2),
            "worst_trade": round(evaluated_trades_df.select(pl.col("absolute_pnl").min()).item(), 2)
        })
        
        # ROI statistics
        roi_stats = evaluated_trades_df.select([
            pl.col("roi_percent").mean().alias("avg_roi"),
            pl.col("roi_percent").std().alias("roi_std"),
            pl.col("roi_percent").max().alias("max_roi"),
            pl.col("roi_percent").min().alias("min_roi")
        ]).row(0, named=True)
        
        stats.update({
            "avg_roi_percent": round(roi_stats["avg_roi"], 2),
            "roi_volatility": round(roi_stats["roi_std"], 2),
            "max_roi_percent": round(roi_stats["max_roi"], 2),
            "min_roi_percent": round(roi_stats["min_roi"], 2)
        })
        
        # Execution quality statistics
        execution_stats = evaluated_trades_df.select([
            pl.col("execution_score").mean().alias("avg_execution_score"),
            pl.col("total_slippage").mean().alias("avg_slippage"),
            (pl.col("was_signal_accurate").sum() / pl.len() * 100).alias("signal_accuracy_rate")
        ]).row(0, named=True)
        
        stats.update({
            "avg_execution_score": round(execution_stats["avg_execution_score"], 2),
            "avg_slippage_percent": round(execution_stats["avg_slippage"], 2),
            "signal_accuracy_rate": round(execution_stats["signal_accuracy_rate"], 2)
        })
        
        return stats
    
    async def get_trade_insights(self, evaluated_trades_df: pl.DataFrame) -> List[str]:
        """Generate actionable insights from trade evaluation"""
        insights = []
        
        if evaluated_trades_df.is_empty():
            return ["No trades available for analysis."]
        
        stats = await self.calculate_trade_statistics(evaluated_trades_df)
        
        # Win rate insights
        if stats["win_rate"] > 70:
            insights.append(f"🎯 Excellent win rate of {stats['win_rate']}%! Strategy selection is performing well.")
        elif stats["win_rate"] < 40:
            insights.append(f"⚠️ Low win rate of {stats['win_rate']}%. Consider reviewing entry criteria.")
        
        # Execution quality insights
        if stats["avg_execution_score"] < 60:
            insights.append(f"⚙️ Execution quality needs improvement (score: {stats['avg_execution_score']}). Review slippage management.")
        
        # Signal accuracy insights
        if stats["signal_accuracy_rate"] < 50:
            insights.append(f"🎯 Signal accuracy is below 50% ({stats['signal_accuracy_rate']}%). Model may need retraining.")
        
        # Slippage insights
        if stats["avg_slippage_percent"] > 3:
            insights.append(f"💸 High average slippage ({stats['avg_slippage_percent']}%). Consider optimizing execution timing.")
        
        return insights
