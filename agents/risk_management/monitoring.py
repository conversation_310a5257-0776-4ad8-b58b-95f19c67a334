import asyncio
import logging
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class Monitoring:
    """
    Monitors various aspects of the trading system for risk management.
    """
    def __init__(self, config: Dict[str, Any], agent_instance: Any, send_alert_callback: callable):
        self.config = config
        self.agent_instance = agent_instance # To access agent's capital, etc.
        self._send_alert = send_alert_callback
        logger.info("Monitoring initialized.")

    async def monitor_daily_drawdown(self):
        """
        Monitors daily drawdown and triggers alerts if thresholds are breached.
        """
        logger.debug("Starting daily drawdown monitoring.")
        while self.agent_instance.state_manager.is_running():
            try:
                # Placeholder logic for drawdown monitoring
                # In a real scenario, this would involve tracking capital fluctuations
                # and comparing against configured drawdown limits.
                
                # Example: Check if current capital is significantly lower than daily high
                if self.agent_instance.current_capital < self.agent_instance.daily_high_capital * 0.95: # 5% drawdown
                    drawdown_loss = self.agent_instance.daily_high_capital - self.agent_instance.current_capital
                    drawdown_pct = (drawdown_loss / self.agent_instance.daily_high_capital) * 100
                    
                    self.agent_instance.daily_drawdown_loss = drawdown_loss
                    self.agent_instance.daily_drawdown_pct = drawdown_pct

                    # Get dynamic thresholds from configuration
                    max_daily_drawdown_pct = self.config.get('risk_limits', {}).get('max_daily_drawdown_pct', 0.05) * 100  # Convert to percentage
                    critical_drawdown_pct = max_daily_drawdown_pct * 0.8  # 80% of max as warning threshold

                    if drawdown_pct > max_daily_drawdown_pct:
                        await self._send_alert(
                            "CRITICAL: Daily Drawdown Limit Breached",
                            f"Daily drawdown limit breached: {drawdown_pct:.2f}% (Loss: {drawdown_loss:.2f}). Limit: {max_daily_drawdown_pct:.1f}%"
                        )
                        # Trigger emergency safeguard
                        await self.agent_instance.emergency_safeguard_trigger(f"Daily drawdown exceeded limit: {drawdown_pct:.2f}%")
                    elif drawdown_pct > critical_drawdown_pct:
                        await self._send_alert(
                            "WARNING: High Daily Drawdown",
                            f"Daily drawdown approaching limit: {drawdown_pct:.2f}% (Loss: {drawdown_loss:.2f}). Limit: {max_daily_drawdown_pct:.1f}%"
                        )
                
                await asyncio.sleep(self.config.get('monitoring_interval', 60))
            except asyncio.CancelledError:
                logger.info("Daily drawdown monitoring task cancelled.")
                break
            except Exception as e:
                logger.error(f"❌ [MONITOR DRAWDOWN ERROR] Error in daily drawdown monitoring: {e}", exc_info=True)
                await asyncio.sleep(self.config.get('monitoring_interval', 60)) # Wait before retrying

    async def monitor_portfolio_exposure(self):
        """
        Monitors overall portfolio exposure against configured limits using real market data.
        """
        logger.debug("Starting portfolio exposure monitoring.")
        while self.agent_instance.state_manager.is_running():
            try:
                # Calculate real portfolio exposure from active trades
                active_trades = self.agent_instance.trade_manager.get_active_trades()
                total_exposure = 0.0
                total_max_loss = 0.0

                for trade in active_trades:
                    # Calculate current exposure for each trade
                    current_price = trade.get('current_price', trade.get('entry_price', 0))
                    lot_size = trade.get('lot_size', 0)
                    entry_price = trade.get('entry_price', 0)

                    trade_exposure = current_price * lot_size
                    max_loss_per_trade = entry_price * lot_size  # For long options, max loss is premium paid

                    total_exposure += trade_exposure
                    total_max_loss += max_loss_per_trade

                # Get dynamic thresholds from configuration
                risk_limits = self.config.get('risk_limits', {})
                max_daily_capital_usage_pct = risk_limits.get('max_daily_capital_usage_pct', 0.20)  # 20%
                max_portfolio_exposure = self.agent_instance.total_capital * max_daily_capital_usage_pct

                # Calculate exposure percentages
                exposure_pct = (total_exposure / self.agent_instance.total_capital) * 100 if self.agent_instance.total_capital > 0 else 0
                max_loss_pct = (total_max_loss / self.agent_instance.total_capital) * 100 if self.agent_instance.total_capital > 0 else 0

                # Check exposure limits
                if total_exposure > max_portfolio_exposure:
                    await self._send_alert(
                        "CRITICAL: Portfolio Exposure Limit Exceeded",
                        f"Portfolio exposure: {total_exposure:.2f} ({exposure_pct:.1f}%) exceeds limit: {max_portfolio_exposure:.2f} ({max_daily_capital_usage_pct*100:.1f}%)"
                    )
                elif exposure_pct > (max_daily_capital_usage_pct * 100 * 0.8):  # 80% of limit as warning
                    await self._send_alert(
                        "WARNING: High Portfolio Exposure",
                        f"Portfolio exposure: {total_exposure:.2f} ({exposure_pct:.1f}%) approaching limit: {max_portfolio_exposure:.2f} ({max_daily_capital_usage_pct*100:.1f}%)"
                    )

                # Update agent's daily capital used
                self.agent_instance.daily_capital_used = total_exposure

                logger.debug(f"📊 [EXPOSURE] Portfolio exposure: {total_exposure:.2f} ({exposure_pct:.1f}%), Max potential loss: {total_max_loss:.2f} ({max_loss_pct:.1f}%)")
                
                await asyncio.sleep(self.config.get('monitoring_interval', 60))
            except asyncio.CancelledError:
                logger.info("Portfolio exposure monitoring task cancelled.")
                break
            except Exception as e:
                logger.error(f"❌ [MONITOR EXPOSURE ERROR] Error in portfolio exposure monitoring: {e}", exc_info=True)
                await asyncio.sleep(self.config.get('monitoring_interval', 60))

    async def monitor_greeks_exposure(self):
        """
        Monitors Greek exposures (Delta, Gamma, Theta, Vega) against limits.
        """
        logger.debug("Starting Greeks exposure monitoring.")
        
        # Get configuration for Greeks monitoring with dynamic limits based on portfolio size
        risk_limits = self.config.get('risk_limits', {})
        portfolio_value = self.agent_instance.total_capital

        # Dynamic Greek limits based on portfolio size (as percentage of portfolio)
        max_delta_exposure = portfolio_value * risk_limits.get('max_delta_exposure_pct', 0.10)  # 10% of portfolio
        max_gamma_exposure = portfolio_value * risk_limits.get('max_gamma_exposure_pct', 0.05)  # 5% of portfolio
        max_theta_exposure = portfolio_value * risk_limits.get('max_theta_exposure_pct', 0.02)  # 2% of portfolio (daily decay)
        max_vega_exposure = portfolio_value * risk_limits.get('max_vega_exposure_pct', 0.08)   # 8% of portfolio

        greeks_alert_threshold_pct = risk_limits.get('greeks_alert_threshold_pct', 0.8)  # 80% of max limit

        while self.agent_instance.state_manager.is_running():
            try:
                active_trades = self.agent_instance.trade_manager.get_active_trades()
                
                total_delta = 0.0
                total_gamma = 0.0
                total_theta = 0.0
                total_vega = 0.0
                
                for trade in active_trades:
                    # Get real-time Greeks from trade details (updated by market data enrichment)
                    greeks = trade.get('greeks', {})
                    lot_size = trade.get('lot_size', 1)

                    # Calculate position-weighted Greeks
                    position_delta = greeks.get('delta', 0.0) * lot_size
                    position_gamma = greeks.get('gamma', 0.0) * lot_size
                    position_theta = greeks.get('theta', 0.0) * lot_size
                    position_vega = greeks.get('vega', 0.0) * lot_size

                    total_delta += position_delta
                    total_gamma += position_gamma
                    total_theta += position_theta
                    total_vega += position_vega

                    logger.debug(f"📊 [GREEKS] Trade {trade.get('trade_id', 'N/A')}: Δ={position_delta:.2f}, Γ={position_gamma:.4f}, Θ={position_theta:.2f}, ν={position_vega:.2f}")

                # Log current Greek exposures
                logger.debug(f"📊 [PORTFOLIO_GREEKS] Total: Δ={total_delta:.2f}, Γ={total_gamma:.4f}, Θ={total_theta:.2f}, ν={total_vega:.2f}")

                alert_messages = []
                critical_alerts = []

                # Check Delta exposure (directional risk)
                delta_threshold = max_delta_exposure * greeks_alert_threshold_pct
                if abs(total_delta) > max_delta_exposure:
                    critical_alerts.append(f"CRITICAL: Delta exposure ({total_delta:.2f}) exceeds limit ({max_delta_exposure:.2f})")
                elif abs(total_delta) > delta_threshold:
                    alert_messages.append(f"Delta exposure ({total_delta:.2f}) exceeds {greeks_alert_threshold_pct*100:.0f}% of limit ({max_delta_exposure:.2f})")

                # Check Gamma exposure (convexity risk)
                gamma_threshold = max_gamma_exposure * greeks_alert_threshold_pct
                if abs(total_gamma) > max_gamma_exposure:
                    critical_alerts.append(f"CRITICAL: Gamma exposure ({total_gamma:.4f}) exceeds limit ({max_gamma_exposure:.4f})")
                elif abs(total_gamma) > gamma_threshold:
                    alert_messages.append(f"Gamma exposure ({total_gamma:.4f}) exceeds {greeks_alert_threshold_pct*100:.0f}% of limit ({max_gamma_exposure:.4f})")

                # Check Theta exposure (time decay risk) - negative theta means losing money daily
                theta_threshold = max_theta_exposure * greeks_alert_threshold_pct
                if abs(total_theta) > max_theta_exposure:
                    critical_alerts.append(f"CRITICAL: Theta exposure ({total_theta:.2f}) exceeds limit ({max_theta_exposure:.2f})")
                elif abs(total_theta) > theta_threshold:
                    alert_messages.append(f"Theta exposure ({total_theta:.2f}) exceeds {greeks_alert_threshold_pct*100:.0f}% of limit ({max_theta_exposure:.2f})")

                # Check Vega exposure (volatility risk)
                vega_threshold = max_vega_exposure * greeks_alert_threshold_pct
                if abs(total_vega) > max_vega_exposure:
                    critical_alerts.append(f"CRITICAL: Vega exposure ({total_vega:.2f}) exceeds limit ({max_vega_exposure:.2f})")
                elif abs(total_vega) > vega_threshold:
                    alert_messages.append(f"Vega exposure ({total_vega:.2f}) exceeds {greeks_alert_threshold_pct*100:.0f}% of limit ({max_vega_exposure:.2f})")

                # Send critical alerts first
                if critical_alerts:
                    await self._send_alert(
                        "CRITICAL: Greeks Exposure Limit Exceeded",
                        " | ".join(critical_alerts)
                    )
                    logger.error(f"🚨 [CRITICAL_GREEKS] {' | '.join(critical_alerts)}")

                    # Consider triggering emergency safeguard for critical Greek exposures
                    await self.agent_instance.emergency_safeguard_trigger(f"Critical Greeks exposure: {critical_alerts[0]}")

                # Send warning alerts
                if alert_messages:
                    await self._send_alert(
                        "WARNING: High Greeks Exposure",
                        " | ".join(alert_messages)
                    )
                    logger.warning(f"⚠️ [GREEKS_WARNING] {' | '.join(alert_messages)}")
                else:
                    logger.debug("Greeks exposure within limits.")

                await asyncio.sleep(self.config.get('monitoring_interval', 60))
            except asyncio.CancelledError:
                logger.info("Greeks exposure monitoring task cancelled.")
                break
            except Exception as e:
                logger.error(f"❌ [MONITOR GREEKS ERROR] Error in Greeks exposure monitoring: {e}", exc_info=True)
                await asyncio.sleep(self.config.get('monitoring_interval', 60))

    async def generate_risk_alerts(self):
        """
        Generates consolidated risk alerts based on monitored conditions.
        """
        logger.debug("Starting risk alert generation.")
        
        alert_consolidation_interval = self.config.get('risk_management', {}).get('alert_consolidation_interval', 300) # 5 minutes
        critical_drawdown_threshold = self.config.get('risk_management', {}).get('critical_drawdown_pct', 15.0) # 15%

        while self.agent_instance.state_manager.is_running():
            try:
                # Check for critical conditions that might require immediate attention
                critical_alerts = []
                
                # Check for high daily drawdown
                if self.agent_instance.daily_drawdown_pct > critical_drawdown_threshold:
                    critical_alerts.append(
                        f"CRITICAL: Daily drawdown ({self.agent_instance.daily_drawdown_pct:.2f}%) exceeds critical threshold ({critical_drawdown_threshold:.1f}%)."
                    )
                
                # Check if trading is paused due to an emergency
                if self.agent_instance.trading_paused:
                    critical_alerts.append("CRITICAL: Trading is currently paused.")

                # If there are critical alerts, send a consolidated message
                if critical_alerts:
                    await self._send_alert(
                        "Consolidated Risk Alert",
                        "\n".join(critical_alerts)
                    )
                    logger.warning(f"Consolidated Risk Alert sent: {' '.join(critical_alerts)}")

                await asyncio.sleep(alert_consolidation_interval)
            except asyncio.CancelledError:
                logger.info("Risk alert generation task cancelled.")
                break
            except Exception as e:
                logger.error(f"❌ [GENERATE ALERTS ERROR] Error in risk alert generation: {e}", exc_info=True)
                await asyncio.sleep(alert_consolidation_interval)

    async def broadcast_risk_summary(self):
        """
        Broadcasts a summary of the current risk status.
        """
        logger.debug("Starting risk summary broadcast.")
        while self.agent_instance.state_manager.is_running():
            try:
                # Placeholder for broadcasting risk summary
                # This could be sent to a UI, a dashboard, or other agents.
                summary = {
                    "agent_id": self.agent_instance.agent_id,
                    "current_capital": self.agent_instance.current_capital,
                    "daily_drawdown_pct": self.agent_instance.daily_drawdown_pct,
                    "trading_paused": self.agent_instance.trading_paused,
                    "state": self.agent_instance.state_manager.get_state().name
                }
                logger.info(f"📊 [RISK SUMMARY] {summary}")
                
                await asyncio.sleep(self.config.get('summary_broadcast_interval', 120)) # e.g., every 2 minutes
            except asyncio.CancelledError:
                logger.info("Risk summary broadcast task cancelled.")
                break
            except Exception as e:
                logger.error(f"❌ [BROADCAST SUMMARY ERROR] Error broadcasting risk summary: {e}", exc_info=True)
                await asyncio.sleep(self.config.get('summary_broadcast_interval', 120))
