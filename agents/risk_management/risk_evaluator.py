import asyncio
import logging
from typing import Dict, List, Optional, Any
from agents.risk_management.constants import (
    DEFAULT_RISK_SCORE,
)

logger = logging.getLogger(__name__)

class RiskEvaluator:
    """
    Evaluates trading signals based on risk parameters and configuration.
    """
    def __init__(self, config: Dict[str, Any], agent_instance: Any):
        self.config = config
        self.agent_instance = agent_instance # To access agent's capital, etc.
        logger.info("RiskEvaluator initialized.")

    def _calculate_option_risk_metrics(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculates option-specific risk metrics from the signal using real market data.
        Now uses actual Greeks and market data instead of placeholder values.
        """
        # Get real-time Greeks from the signal (already enriched with market data)
        greeks = signal.get('greeks', {})

        metrics = {
            'implied_volatility': signal.get('implied_volatility', 0.20),
            'delta': greeks.get('delta', 0.0),
            'gamma': greeks.get('gamma', 0.0),
            'theta': greeks.get('theta', 0.0),
            'vega': greeks.get('vega', 0.0),
            'time_to_expiry_days': signal.get('time_to_expiry_days', 0),
            'option_type': signal.get('option_type', 'call'),
            'current_underlying_price': signal.get('current_underlying_price', 0.0),
            'strike_price': signal.get('strike_price', 0.0),
            'entry_price': signal.get('entry_price', 0.0)
        }

        # Calculate additional risk metrics
        metrics.update(self._calculate_advanced_risk_metrics(metrics))

        logger.debug(f"Calculated option risk metrics for signal {signal.get('signal_id', 'N/A')}: {metrics}")
        return metrics

    def _calculate_advanced_risk_metrics(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate advanced risk metrics like probability of ITM/OTM, max loss, etc.
        """
        try:
            advanced_metrics = {}

            underlying_price = metrics.get('current_underlying_price', 0.0)
            strike_price = metrics.get('strike_price', 0.0)
            option_type = metrics.get('option_type', 'call')
            entry_price = metrics.get('entry_price', 0.0)
            time_to_expiry = metrics.get('time_to_expiry_days', 0)

            if underlying_price > 0 and strike_price > 0:
                # Calculate moneyness
                moneyness = underlying_price / strike_price
                advanced_metrics['moneyness'] = moneyness

                # Estimate probability of ITM (simplified)
                if option_type.lower() in ['call', 'ce']:
                    # Call option
                    if moneyness > 1.0:
                        prob_itm = 0.6 + min(0.3, (moneyness - 1.0) * 0.5)
                    else:
                        prob_itm = max(0.1, moneyness * 0.5)
                else:
                    # Put option
                    if moneyness < 1.0:
                        prob_itm = 0.6 + min(0.3, (1.0 - moneyness) * 0.5)
                    else:
                        prob_itm = max(0.1, (2.0 - moneyness) * 0.5)

                advanced_metrics['probability_itm'] = min(0.9, max(0.1, prob_itm))
                advanced_metrics['probability_otm'] = 1.0 - advanced_metrics['probability_itm']

                # Calculate maximum loss (for option buyers, it's the premium paid)
                advanced_metrics['max_loss_per_contract'] = entry_price

                # Calculate breakeven point
                if option_type.lower() in ['call', 'ce']:
                    breakeven = strike_price + entry_price
                else:
                    breakeven = strike_price - entry_price
                advanced_metrics['breakeven_price'] = breakeven

                # Time decay risk (higher theta means more time decay risk)
                theta = abs(metrics.get('theta', 0.0))
                if time_to_expiry > 0:
                    daily_time_decay_pct = (theta / entry_price) * 100 if entry_price > 0 else 0
                    advanced_metrics['daily_time_decay_pct'] = daily_time_decay_pct
                    advanced_metrics['time_decay_risk'] = 'HIGH' if daily_time_decay_pct > 2.0 else 'MEDIUM' if daily_time_decay_pct > 1.0 else 'LOW'
                else:
                    advanced_metrics['time_decay_risk'] = 'EXPIRED'

                # Volatility risk
                vega = abs(metrics.get('vega', 0.0))
                if entry_price > 0:
                    vol_sensitivity_pct = (vega / entry_price) * 100
                    advanced_metrics['volatility_sensitivity_pct'] = vol_sensitivity_pct
                    advanced_metrics['volatility_risk'] = 'HIGH' if vol_sensitivity_pct > 5.0 else 'MEDIUM' if vol_sensitivity_pct > 2.0 else 'LOW'

            return advanced_metrics

        except Exception as e:
            logger.error(f"Error calculating advanced risk metrics: {e}")
            return {}

    async def evaluate_signal_for_risk(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluates a trading signal for risk with detailed assessment logic.
        """
        signal_id = signal.get('signal_id', 'N/A')
        logger.debug(f"Evaluating risk for signal: {signal_id}")

        # --- Configuration Parameters ---
        risk_config = self.config.get('risk_management', {})
        max_capital_allocation_per_trade = risk_config.get('max_capital_allocation_per_trade', 0.05) # 5%
        max_open_positions = risk_config.get('max_open_positions', 5)
        drawdown_limit_pct = risk_config.get('drawdown_limit_pct', 0.10) # 10%
        volatility_threshold = risk_config.get('volatility_threshold', 0.02) # 2%
        max_option_implied_volatility = risk_config.get('max_option_implied_volatility', 0.50) # 50% IV
        max_theta_decay_per_day = risk_config.get('max_theta_decay_per_day', 0.01) # 1% of premium per day

        # --- Current Agent State ---
        current_capital = self.agent_instance.current_capital
        active_trades = self.agent_instance.trade_manager.get_active_trades()
        num_active_trades = len(active_trades)
        
        # Calculate current drawdown percentage
        current_drawdown_pct = (self.agent_instance.daily_high_capital - current_capital) / self.agent_instance.daily_high_capital if self.agent_instance.daily_high_capital else 0.0

        # --- Trade Specifics & Option Metrics ---
        original_lot_size = signal.get('lot_size', 1)
        entry_price = signal.get('entry_price') # This is crucial for options premium
        if not entry_price:
            logger.error(f"Signal {signal_id} missing 'entry_price'. Cannot evaluate risk.")
            return {
                'approved': False, 'downscaled': True, 'reason': "Missing entry price in signal.",
                'risk_score': 1.0, 'adjusted_lot_size': 0, 'llm_explanation': 'Signal rejected due to missing entry price.'
            }

        # Calculate option-specific risk metrics with real market data
        option_metrics = self._calculate_option_risk_metrics(signal)
        current_volatility = option_metrics['implied_volatility']
        option_theta = option_metrics['theta']
        time_to_expiry_days = option_metrics['time_to_expiry_days']

        # Get advanced risk metrics
        probability_itm = option_metrics.get('probability_itm', 0.5)
        max_loss_per_contract = option_metrics.get('max_loss_per_contract', entry_price)
        time_decay_risk = option_metrics.get('time_decay_risk', 'MEDIUM')
        volatility_risk = option_metrics.get('volatility_risk', 'MEDIUM')
        moneyness = option_metrics.get('moneyness', 1.0)

        # Calculate initial position size based on risk_per_trade_pct from config
        risk_per_trade_pct = risk_config.get('risk_per_trade_pct', 0.01) # 1% of capital per trade
        calculated_lot_size = self.agent_instance.trade_manager.calculate_position_size(signal, risk_per_trade_pct)
        
        # If calculated_lot_size is 0, it means the trade is too risky or capital is insufficient
        if calculated_lot_size == 0:
            return {
                'approved': False, 'downscaled': True, 'reason': "Calculated position size is zero (too risky or insufficient capital).",
                'risk_score': 0.95, 'adjusted_lot_size': 0, 'llm_explanation': 'Signal rejected due to calculated position size being zero.'
            }

        # Use the calculated lot size for further checks, or original if it's smaller and allowed
        adjusted_lot_size = min(original_lot_size, calculated_lot_size)
        trade_capital_required = adjusted_lot_size * entry_price # Total premium paid for the position

        # --- Decision Variables ---
        approved = True
        risk_score = DEFAULT_RISK_SCORE
        reason = "Signal approved by default."
        
        # --- Risk Assessment Checks ---

        # 1. Capital Allocation Check (using adjusted lot size)
        if current_capital > 0 and (trade_capital_required / current_capital) > max_capital_allocation_per_trade:
            approved = False
            reason = f"Trade capital ({trade_capital_required:.2f}) exceeds allocation limit ({max_capital_allocation_per_trade*100:.1f}% of capital)."
            risk_score = 0.7
            adjusted_lot_size = max(0, int(adjusted_lot_size * (max_capital_allocation_per_trade * current_capital / trade_capital_required)))
            logger.warning(f"Risk Check Failed ({signal_id}): Capital Allocation. Adjusted lot size to {adjusted_lot_size}.")

        # 2. Open Positions Check
        if approved and num_active_trades >= max_open_positions:
            approved = False
            reason = f"Exceeds maximum open positions ({max_open_positions})."
            risk_score = 0.6
            adjusted_lot_size = 0
            logger.warning(f"Risk Check Failed ({signal_id}): Max Open Positions. Adjusted lot size to {adjusted_lot_size}.")

        # 3. Drawdown Check (considering potential loss from this trade)
        # A more accurate check would consider the potential max loss from this trade (e.g., premium paid for long options)
        # For now, we use trade_capital_required as the max potential loss for simplicity in this check.
        potential_capital_after_trade_loss = current_capital - trade_capital_required # Max loss for long options
        if approved and potential_capital_after_trade_loss > 0 and (self.agent_instance.daily_high_capital - potential_capital_after_trade_loss) / self.agent_instance.daily_high_capital > drawdown_limit_pct:
            approved = False
            reason = f"Trade would exceed daily drawdown limit ({drawdown_limit_pct*100:.1f}%)."
            risk_score = 0.8
            adjusted_lot_size = 0
            logger.warning(f"Risk Check Failed ({signal_id}): Drawdown Limit. Adjusted lot size to {adjusted_lot_size}.")
        
        # 4. Implied Volatility Check (Option-specific)
        if approved and current_volatility > max_option_implied_volatility:
            approved = False
            reason = f"Implied Volatility ({current_volatility*100:.1f}%) exceeds threshold ({max_option_implied_volatility*100:.1f}%)."
            risk_score = 0.75
            adjusted_lot_size = 0
            logger.warning(f"Risk Check Failed ({signal_id}): High Implied Volatility. Adjusted lot size to {adjusted_lot_size}.")

        # 5. Theta Decay Check (Option-specific)
        # For long options, high negative theta means faster decay.
        # For short options, high positive theta means faster decay (beneficial).
        # Assuming we are primarily buying options, we want to avoid high negative theta.
        if approved and option_theta < 0 and abs(option_theta * adjusted_lot_size) > (trade_capital_required * max_theta_decay_per_day):
            approved = False
            reason = f"Excessive Theta decay ({option_theta:.4f} per day) for option position."
            risk_score = 0.65
            adjusted_lot_size = 0
            logger.warning(f"Risk Check Failed ({signal_id}): Excessive Theta Decay. Adjusted lot size to {adjusted_lot_size}.")

        # 6. Time to Expiry Check (Option-specific)
        min_time_to_expiry_days = risk_config.get('min_time_to_expiry_days', 7) # e.g., at least 7 days to expiry
        if approved and time_to_expiry_days < min_time_to_expiry_days:
            approved = False
            reason = f"Time to expiry ({time_to_expiry_days} days) is less than minimum required ({min_time_to_expiry_days} days)."
            risk_score = 0.55
            adjusted_lot_size = 0
            logger.warning(f"Risk Check Failed ({signal_id}): Short Time to Expiry. Adjusted lot size to {adjusted_lot_size}.")

        # 7. Enhanced Risk Checks using Advanced Metrics

        # 7a. Probability of ITM Check
        if approved and probability_itm < 0.3:
            # Very low probability of ITM - reduce position size
            adjusted_lot_size = max(1, int(adjusted_lot_size * 0.6))
            reason = f"Position size reduced due to low probability of ITM ({probability_itm*100:.1f}%). Adjusted to {adjusted_lot_size} contracts."
            risk_score = max(risk_score, 0.7)
            logger.info(f"Risk Adjustment ({signal_id}): Low probability ITM - position size reduced.")

        # 7b. Moneyness Check
        if approved and (moneyness < 0.8 or moneyness > 1.2):  # Far OTM or deep ITM
            adjusted_lot_size = max(1, int(adjusted_lot_size * 0.8))
            reason = f"Position size reduced due to extreme moneyness ({moneyness:.2f}). Adjusted to {adjusted_lot_size} contracts."
            risk_score = max(risk_score, 0.65)
            logger.info(f"Risk Adjustment ({signal_id}): Extreme moneyness - position size reduced.")

        # 7c. Volatility Risk Assessment
        if approved and volatility_risk == 'HIGH':
            adjusted_lot_size = max(1, int(adjusted_lot_size * 0.5))
            reason = f"Position size reduced due to high volatility risk. Adjusted to {adjusted_lot_size} contracts."
            risk_score = max(risk_score, 0.6)
            logger.info(f"Risk Adjustment ({signal_id}): High volatility risk - position size reduced.")

        # 7d. Time Decay Risk Assessment
        if approved and time_decay_risk == 'HIGH' and time_to_expiry_days < 7:
            adjusted_lot_size = max(1, int(adjusted_lot_size * 0.7))
            reason = f"Position size reduced due to high time decay risk near expiry. Adjusted to {adjusted_lot_size} contracts."
            risk_score = max(risk_score, 0.55)
            logger.info(f"Risk Adjustment ({signal_id}): High time decay risk - position size reduced.")

        # --- Final Decision ---
        if not approved:
            # If any check failed, ensure lot size is 0 if it was not already adjusted to 0
            if adjusted_lot_size > 0:
                adjusted_lot_size = 0
                reason = "Trade blocked due to risk assessment."
                risk_score = max(risk_score, 0.9) # Ensure high risk score if blocked

        # Ensure lot size is not negative
        adjusted_lot_size = max(0, adjusted_lot_size)

        decision = {
            'approved': approved,
            'downscaled': adjusted_lot_size < original_lot_size,
            'reason': reason,
            'risk_score': risk_score,
            'adjusted_lot_size': adjusted_lot_size,
            'llm_explanation': f"Risk assessment for signal {signal_id}: {reason}"
        }
        logger.debug(f"Risk evaluation decision for signal {signal_id}: {decision}")
        return decision
