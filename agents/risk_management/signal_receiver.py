#!/usr/bin/env python3
"""
Signal Receiver Module for Options Risk Management Agent
Handles real-time signal reception from signal generation agents via websocket/message queue
"""

import asyncio
import json
import logging
import websockets
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
import aiofiles
from collections import deque

logger = logging.getLogger(__name__)

class SignalReceiver:
    """
    Handles real-time signal reception from signal generation agents.
    Supports both websocket and file-based signal reception.
    """
    
    def __init__(self, config: Dict[str, Any], signal_callback: Callable):
        self.config = config
        self.signal_callback = signal_callback  # Callback to process received signals
        self.is_running = False
        
        # Signal queue for buffering
        self.signal_queue = asyncio.Queue(maxsize=100)
        self.processed_signals = deque(maxlen=1000)  # Keep track of processed signals
        
        # File-based signal monitoring
        self.signals_path = Path("data/signals")
        self.signals_path.mkdir(parents=True, exist_ok=True)
        self.last_processed_file = None
        
        # WebSocket connection
        self.websocket_uri = config.get('websocket_uri', 'ws://localhost:8765')
        self.websocket_connection = None
        
        # Statistics
        self.signals_received = 0
        self.signals_processed = 0
        self.last_signal_time = None
        
        logger.info("🔗 [INIT] Signal Receiver initialized")
    
    async def start(self):
        """Start the signal receiver"""
        try:
            logger.info("🚀 [START] Starting Signal Receiver...")
            self.is_running = True
            
            # Start signal processing tasks
            tasks = [
                asyncio.create_task(self._process_signal_queue()),
                asyncio.create_task(self._monitor_signal_files()),
            ]
            
            # Try to start websocket connection if configured
            if self.config.get('enable_websocket', False):
                tasks.append(asyncio.create_task(self._websocket_listener()))
            
            logger.info("✅ [SUCCESS] Signal Receiver started successfully")
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to start Signal Receiver: {e}", exc_info=True)
            self.is_running = False
    
    async def stop(self):
        """Stop the signal receiver"""
        logger.info("🛑 [STOP] Stopping Signal Receiver...")
        self.is_running = False
        
        if self.websocket_connection:
            await self.websocket_connection.close()
        
        logger.info("✅ [SUCCESS] Signal Receiver stopped")
    
    async def _websocket_listener(self):
        """Listen for signals via WebSocket"""
        while self.is_running:
            try:
                logger.info(f"🔌 [WEBSOCKET] Connecting to {self.websocket_uri}...")
                async with websockets.connect(self.websocket_uri) as websocket:
                    self.websocket_connection = websocket
                    logger.info("✅ [WEBSOCKET] Connected successfully")
                    
                    async for message in websocket:
                        if not self.is_running:
                            break
                        
                        try:
                            signal_data = json.loads(message)
                            await self._queue_signal(signal_data, source='websocket')
                        except json.JSONDecodeError as e:
                            logger.error(f"❌ [WEBSOCKET] Invalid JSON received: {e}")
                        except Exception as e:
                            logger.error(f"❌ [WEBSOCKET] Error processing message: {e}")
                            
            except websockets.exceptions.ConnectionClosed:
                logger.warning("⚠️ [WEBSOCKET] Connection closed, attempting to reconnect...")
                await asyncio.sleep(5)
            except Exception as e:
                logger.error(f"❌ [WEBSOCKET] Connection error: {e}")
                await asyncio.sleep(10)
    
    async def _monitor_signal_files(self):
        """Monitor signal files for new signals"""
        while self.is_running:
            try:
                # Look for new signal files
                signal_files = list(self.signals_path.glob("final_consolidated_*.parquet"))
                
                if signal_files:
                    # Get the most recent file
                    latest_file = max(signal_files, key=lambda x: x.stat().st_mtime)
                    
                    # Check if this is a new file
                    if self.last_processed_file != latest_file:
                        await self._process_signal_file(latest_file)
                        self.last_processed_file = latest_file
                
                await asyncio.sleep(self.config.get('file_monitor_interval', 5))
                
            except Exception as e:
                logger.error(f"❌ [FILE_MONITOR] Error monitoring signal files: {e}")
                await asyncio.sleep(10)
    
    async def _process_signal_file(self, file_path: Path):
        """Process signals from a parquet file"""
        try:
            import polars as pl
            
            logger.info(f"📄 [FILE] Processing signal file: {file_path.name}")
            df = pl.read_parquet(file_path)
            
            if df.height > 0:
                # Convert each row to a signal dictionary
                for row in df.iter_rows(named=True):
                    signal_data = dict(row)
                    # Add metadata
                    signal_data['source_file'] = str(file_path)
                    signal_data['received_at'] = datetime.now().isoformat()
                    
                    await self._queue_signal(signal_data, source='file')
                
                logger.info(f"✅ [FILE] Queued {df.height} signals from {file_path.name}")
            else:
                logger.debug(f"📄 [FILE] No signals found in {file_path.name}")
                
        except Exception as e:
            logger.error(f"❌ [FILE] Error processing signal file {file_path}: {e}")
    
    async def _queue_signal(self, signal_data: Dict[str, Any], source: str):
        """Queue a signal for processing"""
        try:
            # Add metadata
            signal_data['source'] = source
            signal_data['queued_at'] = datetime.now().isoformat()
            
            # Try to add to queue (non-blocking)
            try:
                self.signal_queue.put_nowait(signal_data)
                self.signals_received += 1
                self.last_signal_time = datetime.now()
                logger.debug(f"📥 [QUEUE] Signal queued from {source}: {signal_data.get('signal_id', 'N/A')}")
            except asyncio.QueueFull:
                logger.warning("⚠️ [QUEUE] Signal queue is full, dropping oldest signal")
                # Remove oldest signal and add new one
                try:
                    self.signal_queue.get_nowait()
                    self.signal_queue.put_nowait(signal_data)
                except asyncio.QueueEmpty:
                    pass
                    
        except Exception as e:
            logger.error(f"❌ [QUEUE] Error queuing signal: {e}")
    
    async def _process_signal_queue(self):
        """Process signals from the queue"""
        while self.is_running:
            try:
                # Wait for a signal with timeout
                signal_data = await asyncio.wait_for(
                    self.signal_queue.get(), 
                    timeout=1.0
                )
                
                # Process the signal
                await self._process_signal(signal_data)
                self.signal_queue.task_done()
                
            except asyncio.TimeoutError:
                # No signal received, continue
                continue
            except Exception as e:
                logger.error(f"❌ [PROCESS] Error processing signal queue: {e}")
                await asyncio.sleep(1)
    
    async def _process_signal(self, signal_data: Dict[str, Any]):
        """Process a single signal"""
        try:
            signal_id = signal_data.get('signal_id', f"SIG_{datetime.now().strftime('%H%M%S%f')}")
            
            # Check if we've already processed this signal
            if signal_id in [s.get('signal_id') for s in self.processed_signals]:
                logger.debug(f"🔄 [PROCESS] Signal {signal_id} already processed, skipping")
                return
            
            logger.info(f"⚡ [PROCESS] Processing signal: {signal_id}")
            
            # Validate signal data
            if not self._validate_signal(signal_data):
                logger.warning(f"⚠️ [VALIDATE] Invalid signal data for {signal_id}")
                return
            
            # Call the callback to process the signal
            await self.signal_callback(signal_data)
            
            # Track processed signal
            signal_data['processed_at'] = datetime.now().isoformat()
            self.processed_signals.append(signal_data)
            self.signals_processed += 1
            
            logger.info(f"✅ [PROCESS] Signal {signal_id} processed successfully")
            
        except Exception as e:
            logger.error(f"❌ [PROCESS] Error processing signal: {e}", exc_info=True)
    
    def _validate_signal(self, signal_data: Dict[str, Any]) -> bool:
        """Validate signal data structure"""
        required_fields = ['signal_id', 'underlying', 'action', 'option_type']
        
        for field in required_fields:
            if field not in signal_data:
                logger.warning(f"⚠️ [VALIDATE] Missing required field: {field}")
                return False
        
        # Additional validation
        if signal_data.get('action') not in ['BUY', 'SELL', 'HOLD']:
            logger.warning(f"⚠️ [VALIDATE] Invalid action: {signal_data.get('action')}")
            return False
        
        if signal_data.get('option_type') not in ['call', 'put', 'CE', 'PE']:
            logger.warning(f"⚠️ [VALIDATE] Invalid option_type: {signal_data.get('option_type')}")
            return False
        
        return True
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get receiver statistics"""
        return {
            'signals_received': self.signals_received,
            'signals_processed': self.signals_processed,
            'queue_size': self.signal_queue.qsize(),
            'last_signal_time': self.last_signal_time.isoformat() if self.last_signal_time else None,
            'is_running': self.is_running
        }
