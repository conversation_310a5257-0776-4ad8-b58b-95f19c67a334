import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import deque

from agents.risk_management.constants import (
    DEFAULT_LOT_SIZE,
)

logger = logging.getLogger(__name__)

class TradeManager:
    """
    Manages active trades, trade history, and related metrics.
    """
    def __init__(self, config_manager: Any, state_manager: Any, emergency_trigger: callable):
        self.config_manager = config_manager
        self.state_manager = state_manager
        self.emergency_safeguard_trigger = emergency_trigger
        
        self.current_capital: float = 0.0 # This will be updated by the agent
        self.agent_id: str = "" # This will be set by the agent

        self.active_trades: Dict[str, Dict[str, Any]] = {} # Trade ID -> Trade details
        self.trade_history: List[Dict[str, Any]] = [] # List of all trade events

        # Metrics for performance tracking
        self.win_loss_streak: deque = deque(maxlen=10) # Track consecutive wins/losses
        self.consecutive_sl_hits: int = 0 # Count of consecutive stop-loss hits
        self.last_trade_time: Dict[str, datetime] = {} # Timestamp of last trade for a symbol/strategy

        logger.info("TradeManager initialized.")

    def set_agent_capital_and_id(self, capital: float, agent_id: str):
        """Sets the agent's capital and ID for trade management."""
        self.current_capital = capital
        self.agent_id = agent_id
        logger.debug(f"TradeManager set with capital: {self.current_capital:.2f}, Agent ID: {self.agent_id}")

    async def log_trade_decision(self, signal: Dict[str, Any], decision: Dict[str, Any]):
        """Logs the decision made for a given trading signal."""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "signal_id": signal.get("signal_id", "N/A"),
            "symbol": signal.get("symbol", "N/A"),
            "decision": decision,
            "agent_id": self.agent_id
        }
        self.trade_history.append(log_entry)
        logger.info(f"Trade decision logged for {signal.get('symbol', 'N/A')}: Approved={decision.get('approved')}, LotSize={decision.get('adjusted_lot_size')}")
        
        # Update capital if trade is approved and lot size is positive
        if decision.get('approved') and decision.get('adjusted_lot_size', 0) > 0:
            # This is a simplified capital update. Real logic would involve trade cost, etc.
            # For now, we assume lot size directly impacts capital used.
            # A more accurate approach would be to track actual trade execution.
            pass # Capital update should happen upon actual trade execution

    async def update_trade_status(self, trade_update: Dict[str, Any]):
        """
        Updates the status of an active trade.
        Example trade_update: {"trade_id": "...", "status": "closed", "pnl": 100.50}
        """
        trade_id = trade_update.get("trade_id")
        if not trade_id or trade_id not in self.active_trades:
            logger.warning(f"Attempted to update status for non-existent or invalid trade ID: {trade_id}")
            return

        trade = self.active_trades[trade_id]
        old_status = trade.get("status")
        new_status = trade_update.get("status")
        pnl = trade_update.get("pnl")

        trade.update(trade_update) # Apply all updates

        if new_status == "closed":
            logger.info(f"Trade {trade_id} ({trade.get('symbol')}) closed. PnL: {pnl:.2f}")
            
            # Update capital based on PnL
            self.current_capital += pnl
            
            # Update trade history with closure details
            closure_log = {
                "timestamp": datetime.now().isoformat(),
                "event_type": "TRADE_CLOSED",
                "trade_id": trade_id,
                "symbol": trade.get("symbol"),
                "pnl": pnl,
                "agent_id": self.agent_id
            }
            self.trade_history.append(closure_log)

            # Update win/loss streak and reset consecutive SL hits
            if pnl > 0:
                self.win_loss_streak.append("win")
                self.consecutive_sl_hits = 0
            else:
                self.win_loss_streak.append("loss")
                if trade.get("stop_loss_hit"): # Check if it was specifically a stop loss hit
                    self.consecutive_sl_hits += 1
            
            # Remove from active trades
            del self.active_trades[trade_id]

            # Check for emergency safeguard trigger based on consecutive SL hits
            max_sl_hits = self.config_manager.get('max_consecutive_stop_loss_hits', 3)
            if self.consecutive_sl_hits >= max_sl_hits:
                await self.emergency_safeguard_trigger(f"Exceeded maximum consecutive stop-loss hits ({self.consecutive_sl_hits})")

        elif new_status == "open":
            # If a trade is marked as 'open', add it to active trades
            # This might happen if a trade was previously in a pending state
            self.active_trades[trade_id] = trade
            logger.info(f"Trade {trade_id} ({trade.get('symbol')}) is now active.")
            
            # Log the opening event
            opening_log = {
                "timestamp": datetime.now().isoformat(),
                "event_type": "TRADE_OPENED",
                "trade_id": trade_id,
                "symbol": trade.get("symbol"),
                "entry_price": trade.get("entry_price"),
                "lot_size": trade.get("lot_size"),
                "agent_id": self.agent_id
            }
            self.trade_history.append(opening_log)

        # Update last trade time for the symbol
        symbol = trade.get("symbol")
        if symbol:
            self.last_trade_time[symbol] = datetime.now()

        logger.debug(f"Trade {trade_id} status updated to {new_status}. Current capital: {self.current_capital:.2f}")

    def get_active_trades(self) -> List[Dict[str, Any]]:
        """Returns a list of currently active trades."""
        return list(self.active_trades.values())

    def get_trade_history(self) -> List[Dict[str, Any]]:
        """Returns the complete trade history."""
        return self.trade_history

    def add_active_trade(self, trade_details: Dict[str, Any]):
        """Adds a new trade to the active trades list."""
        # Ensure trade has a unique ID, symbol, entry price, lot size, etc.
        trade_id = trade_details.get("trade_id")
        if not trade_id:
            trade_id = f"trade_{len(self.active_trades)}_{datetime.now().timestamp()}"
            trade_details["trade_id"] = trade_id
            
        if trade_id in self.active_trades:
            logger.warning(f"Trade ID {trade_id} already exists. Overwriting active trade.")
        
        self.active_trades[trade_id] = trade_details
        logger.info(f"Added active trade: {trade_id} ({trade_details.get('symbol')})")
        
        # Log the opening event
        opening_log = {
            "timestamp": datetime.now().isoformat(),
            "event_type": "TRADE_OPENED",
            "trade_id": trade_id,
            "symbol": trade_details.get("symbol"),
            "entry_price": trade_details.get("entry_price"),
            "lot_size": trade_details.get("lot_size"),
            "agent_id": self.agent_id
        }
        self.trade_history.append(opening_log)

    def remove_active_trade(self, trade_id: str):
        """Removes a trade from the active trades list."""
        if trade_id in self.active_trades:
            del self.active_trades[trade_id]
            logger.info(f"Removed active trade: {trade_id}")
        else:
            logger.warning(f"Attempted to remove non-existent active trade ID: {trade_id}")

    def calculate_position_size(self, signal: Dict[str, Any], risk_per_trade_pct: float) -> int:
        """
        Calculates the optimal position size based on risk parameters.
        
        Args:
            signal (Dict[str, Any]): The trading signal with relevant price information.
            risk_per_trade_pct (float): The percentage of capital to risk per trade (e.g., 0.01 for 1%).
            
        Returns:
            int: The calculated lot size.
        """
        if not self.current_capital or self.current_capital <= 0:
            logger.warning("Cannot calculate position size: current capital is zero or negative.")
            return 0

        # Get stop loss from signal or config. This is crucial for position sizing.
        # Assuming signal provides a 'stop_loss_price' or 'max_loss_per_share'
        # For options, this might be the premium paid or a specific percentage of premium.
        # For simplicity, let's assume a fixed percentage of the entry price as potential loss per share/contract.
        entry_price = signal.get('entry_price')
        if not entry_price:
            logger.warning(f"Cannot calculate position size for signal {signal.get('signal_id', 'N/A')}: entry_price missing.")
            return 0

        # Determine the maximum acceptable loss per contract/share
        # This is a critical part and needs to be robust.
        # For options, max loss is often the premium paid, or a defined stop loss.
        # Let's assume a 'stop_loss_price' is provided in the signal, or a default percentage.
        stop_loss_price = signal.get('stop_loss_price')
        if stop_loss_price is not None:
            potential_loss_per_unit = abs(entry_price - stop_loss_price)
        else:
            # If no explicit stop loss, use a default percentage of entry price as max risk per unit
            default_stop_loss_pct = self.config_manager.get('risk_management', {}).get('default_stop_loss_pct', 0.05) # 5%
            potential_loss_per_unit = entry_price * default_stop_loss_pct
            logger.debug(f"No explicit stop loss for signal {signal.get('signal_id', 'N/A')}. Using default {default_stop_loss_pct*100:.1f}% of entry price as potential loss per unit: {potential_loss_per_unit:.2f}")

        if potential_loss_per_unit <= 0:
            logger.warning(f"Cannot calculate position size: potential loss per unit is zero or negative for signal {signal.get('signal_id', 'N/A')}.")
            return 0

        # Calculate maximum total risk allowed for this trade
        max_risk_amount = self.current_capital * risk_per_trade_pct

        # Calculate lot size
        lot_size = int(max_risk_amount / potential_loss_per_unit)
        
        # Ensure lot size is at least 1 if a trade is to be made, unless max_risk_amount is too small
        lot_size = max(0, lot_size)
        
        logger.info(f"Calculated position size for signal {signal.get('signal_id', 'N/A')}: {lot_size} (Max Risk: {max_risk_amount:.2f}, Loss per unit: {potential_loss_per_unit:.2f})")
        return lot_size

    def set_trade_exit_parameters(self, trade_id: str, stop_loss_price: Optional[float] = None, take_profit_price: Optional[float] = None, trailing_stop_pct: Optional[float] = None):
        """
        Sets stop loss, take profit, and trailing stop parameters for an active trade.
        """
        if trade_id not in self.active_trades:
            logger.warning(f"Cannot set exit parameters for non-existent trade ID: {trade_id}")
            return

        trade = self.active_trades[trade_id]
        if stop_loss_price is not None:
            trade['stop_loss_price'] = stop_loss_price
            logger.debug(f"Trade {trade_id}: Stop Loss set to {stop_loss_price:.2f}")
        if take_profit_price is not None:
            trade['take_profit_price'] = take_profit_price
            logger.debug(f"Trade {trade_id}: Take Profit set to {take_profit_price:.2f}")
        if trailing_stop_pct is not None:
            trade['trailing_stop_pct'] = trailing_stop_pct
            trade['trailing_stop_price'] = trade.get('current_price', trade['entry_price']) * (1 - trailing_stop_pct) if trade.get('current_price', trade['entry_price']) else None
            logger.debug(f"Trade {trade_id}: Trailing Stop set to {trailing_stop_pct*100:.1f}% (initial price: {trade.get('trailing_stop_price'):.2f})")

    async def check_exit_conditions(self, trade_id: str, current_price: float) -> Optional[str]:
        """
        Checks if stop loss, take profit, or trailing stop conditions are met for a trade.
        
        Args:
            trade_id (str): The ID of the trade to check.
            current_price (float): The current market price of the underlying asset.
            
        Returns:
            Optional[str]: "STOP_LOSS", "TAKE_PROFIT", "TRAILING_STOP" if an exit condition is met, else None.
        """
        if trade_id not in self.active_trades:
            logger.warning(f"Cannot check exit conditions for non-existent trade ID: {trade_id}")
            return None

        trade = self.active_trades[trade_id]
        trade['current_price'] = current_price # Update current price for checks

        # Stop Loss Check
        stop_loss_price = trade.get('stop_loss_price')
        if stop_loss_price is not None:
            if current_price <= stop_loss_price:
                logger.info(f"Trade {trade_id} hit Stop Loss at {current_price:.2f} (SL: {stop_loss_price:.2f})")
                trade['stop_loss_hit'] = True
                return "STOP_LOSS"

        # Take Profit Check
        take_profit_price = trade.get('take_profit_price')
        if take_profit_price is not None:
            if current_price >= take_profit_price:
                logger.info(f"Trade {trade_id} hit Take Profit at {current_price:.2f} (TP: {take_profit_price:.2f})")
                return "TAKE_PROFIT"

        # Trailing Stop Check
        trailing_stop_pct = trade.get('trailing_stop_pct')
        if trailing_stop_pct is not None:
            entry_price = trade.get('entry_price', current_price) # Use current_price if entry_price not available
            
            # Update trailing stop price if current price makes a new high
            if 'highest_price_since_entry' not in trade or current_price > trade['highest_price_since_entry']:
                trade['highest_price_since_entry'] = current_price
                trade['trailing_stop_price'] = current_price * (1 - trailing_stop_pct)
                logger.debug(f"Trade {trade_id}: Trailing stop updated to {trade['trailing_stop_price']:.2f} (New High: {current_price:.2f})")
            
            # Check if current price falls below trailing stop
            if 'trailing_stop_price' in trade and current_price <= trade['trailing_stop_price']:
                logger.info(f"Trade {trade_id} hit Trailing Stop at {current_price:.2f} (TS: {trade['trailing_stop_price']:.2f})")
                return "TRAILING_STOP"
        
        return None
