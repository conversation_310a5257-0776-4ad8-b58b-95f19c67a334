# Options Performance Analysis Agent Configuration
# This file contains all configuration parameters for the performance analysis system

# Data file paths configuration
data_paths:
  trades_data: "data/trades/completed_trades.parquet"
  historical_data: "data/historical/underlying_data.parquet"
  greeks_data: "data/greeks/options_greeks.parquet"
  export_path: "exports"
  performance_data_path: "data/performance"
  model_baselines_path: "data/performance/model_baselines.json"

# Analysis settings and parameters
analysis_settings:
  # Analysis execution interval in seconds
  analysis_interval: 300

  # Risk-free rate for Sharpe ratio calculations (annual)
  risk_free_rate: 0.06

  # Minimum number of trades required for meaningful analysis
  min_trades_for_analysis: 10

  # Monte Carlo simulation parameters
  monte_carlo_simulations: 10000

  # Confidence levels for VaR calculations
  confidence_levels: [0.95, 0.99]

  # Annualization factors for different time periods
  annualization_factors:
    daily: 252    # Trading days in a year
    weekly: 52    # Weeks in a year
    monthly: 12   # Months in a year

  # Performance calculation parameters
  performance_params:
    sharpe_ratio_threshold: 1.0
    sortino_ratio_threshold: 1.2
    calmar_ratio_threshold: 0.5
    max_drawdown_threshold: 0.15  # 15%
    profit_factor_threshold: 1.5

# Notification and alerting settings
notification_settings:
  # Enable Windows toast notifications
  enable_windows_notifications: true

  # Enable email alerts (requires email configuration)
  enable_email_alerts: false

  # Alert thresholds for various metrics
  alert_thresholds:
    # Maximum drawdown threshold (as decimal)
    max_drawdown: 0.15

    # Daily loss threshold (as decimal)
    daily_loss: 0.05

    # Win rate drop threshold (as decimal)
    win_rate_drop: 0.10

    # Sharpe ratio drop threshold
    sharpe_drop: 0.5

    # ROI prediction error threshold
    roi_prediction_error: 0.20

    # Model accuracy drop threshold
    accuracy_drop: 0.10

    # Capital at risk breach threshold
    capital_breach: 1.1  # 110% of allowed capital

  # Notification frequency limits
  notification_limits:
    max_notifications_per_hour: 10
    cooldown_period_minutes: 15

# Performance optimization settings
performance:
  # Maximum number of worker threads
  max_workers: 5

  # Enable lazy loading of heavy modules
  enable_lazy_loading: true

  # Cache size for frequently accessed data
  cache_size: 1000

  # Batch size for processing large datasets
  batch_size: 1000

  # Memory management settings
  memory_management:
    max_memory_usage_gb: 4
    garbage_collection_threshold: 0.8

# Trade evaluation parameters
trade_evaluation:
  # Slippage thresholds for categorization
  slippage_thresholds:
    minimal: 0.01     # 1%
    acceptable: 0.03  # 3%
    significant: 0.05 # 5%

  # Execution score parameters
  execution_scoring:
    base_score_minimal_slippage: 90
    base_score_acceptable_slippage: 75
    base_score_significant_slippage: 50
    base_score_excessive_slippage: 25
    signal_accuracy_bonus: 10
    signal_accuracy_penalty: 5

  # Entry precision analysis parameters
  entry_precision:
    ce_default_precision: 0.75
    pe_default_precision: 0.70
    default_precision: 0.65

# Strategy aggregation parameters
strategy_aggregation:
  # Minimum sample size for strategy analysis
  min_sample_size: 30

  # Risk-adjusted return thresholds
  risk_thresholds:
    min_sharpe_ratio: 0.5
    min_win_rate: 0.40
    max_drawdown: 0.20

  # Strategy performance classification
  performance_classification:
    excellent_sharpe: 1.5
    good_sharpe: 1.0
    fair_sharpe: 0.5
    excellent_win_rate: 0.70
    good_win_rate: 0.60
    fair_win_rate: 0.50

# Model monitoring parameters
model_monitoring:
  # Drift detection thresholds
  drift_thresholds:
    accuracy_drop: 0.10      # 10% accuracy drop
    confidence_drift: 0.15   # 15% confidence drift
    roi_prediction_error: 0.20 # 20% ROI prediction error increase

  # Baseline update parameters
  baseline_update:
    learning_rate: 0.1  # Alpha for exponential moving average
    min_samples_for_update: 50

  # Model health score weights
  health_score_weights:
    accuracy_score: 0.5
    roi_score: 0.3
    confidence_score: 0.2

  # Retraining triggers
  retraining_triggers:
    accuracy_threshold: 0.55
    roi_correlation_threshold: 0.3
    confidence_discrimination_threshold: 0.1

# Regime analysis parameters
regime_analysis:
  # Regime categories and classifications
  regime_categories:
    market_regime: ["bullish", "bearish", "sideways", "volatile"]
    volatility_regime: ["low_vol", "medium_vol", "high_vol", "extreme_vol"]
    time_regime: ["morning", "midday", "afternoon", "closing"]
    expiry_regime: ["far", "medium", "near", "expiry_day"]

  # Time-based regime definitions
  time_definitions:
    morning: [9, 11]    # 9 AM to 11 AM
    midday: [11, 13]    # 11 AM to 1 PM
    afternoon: [13, 15] # 1 PM to 3 PM
    closing: [15, 16]   # 3 PM to 4 PM

  # Minimum trades required for regime analysis
  min_trades_per_regime: 5

  # Cross-regime analysis parameters
  cross_regime_analysis:
    min_trades_for_combination: 3
    significance_threshold: 0.6  # Win rate threshold for significance

# Risk management parameters
risk_management:
  # Risk control thresholds
  risk_thresholds:
    capital_at_risk_breach: 1.1    # 110% of allowed capital
    daily_drawdown_warning: 0.8    # 80% of daily threshold
    consecutive_losses: 3           # 3 consecutive losses
    win_rate_decline: 0.15         # 15% decline in win rate

  # Trading pause effectiveness parameters
  pause_effectiveness:
    min_effectiveness_score: 70
    max_missed_pauses: 2

  # Risk score calculation weights
  risk_score_weights:
    capital_management: 0.4
    drawdown_control: 0.3
    pause_effectiveness: 0.2
    signal_filtering: 0.1

  # Risk level classifications
  risk_levels:
    low_risk_threshold: 80
    moderate_risk_threshold: 60
    high_risk_threshold: 40

# Export and reporting settings
export_settings:
  # Default export formats
  default_formats: ["excel", "json"]

  # Excel export parameters
  excel_export:
    include_charts: true
    sheet_name_max_length: 31
    include_summary_sheet: true

  # JSON export parameters
  json_export:
    pretty_print: true
    include_metadata: true

  # Report generation settings
  report_generation:
    include_insights: true
    include_recommendations: true
    include_visualizations: true
    max_insights: 10
    max_recommendations: 15

# Logging and debugging settings
logging:
  # Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
  level: "INFO"

  # Log file settings
  file_logging:
    enabled: true
    log_file: "logs/performance_analysis.log"
    max_file_size_mb: 10
    backup_count: 5

  # Performance logging
  performance_logging:
    enabled: true
    log_execution_times: true
    log_memory_usage: true

# Feature flags for experimental features
feature_flags:
  enable_advanced_visualizations: true
  enable_natural_language_queries: true
  enable_real_time_monitoring: false
  enable_machine_learning_insights: true
  enable_cross_strategy_analysis: true
