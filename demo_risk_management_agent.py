#!/usr/bin/env python3
"""
Demo Script for Options Risk Management Agent
Demonstrates the agent working with real data instead of placeholder/mock data
"""

import asyncio
import logging
import polars as pl
from datetime import datetime, timedelta
from pathlib import Path

from agents.options_risk_management_agent import OptionsRiskManagementAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def create_sample_market_data():
    """Create sample market data files to simulate live data feed"""
    logger.info("📊 Creating sample market data...")
    
    # Create data directories
    data_dirs = [
        'data/live/1min',
        'data/live/5min',
        'data/signals'
    ]
    
    for dir_path in data_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    # Create sample NIFTY data
    timestamps = [datetime.now() - timedelta(minutes=i) for i in range(60, 0, -1)]
    base_price = 22000
    
    market_data = pl.DataFrame({
        'timestamp': timestamps,
        'open': [base_price + i + (i % 5) for i in range(60)],
        'high': [base_price + i + 10 + (i % 5) for i in range(60)],
        'low': [base_price + i - 5 + (i % 5) for i in range(60)],
        'close': [base_price + i + 5 + (i % 5) for i in range(60)],
        'volume': [1000 + i * 50 for i in range(60)]
    })
    
    # Save market data files
    for timeframe in ['1min', '5min']:
        file_path = Path(f'data/live/{timeframe}/Index_NIFTY_latest.parquet')
        market_data.write_parquet(file_path)
        logger.info(f"✅ Created market data: {file_path}")


async def create_sample_signals():
    """Create sample trading signals to demonstrate signal processing"""
    logger.info("📨 Creating sample trading signals...")
    
    # Create realistic trading signals
    signals_data = pl.DataFrame({
        'signal_id': ['DEMO_001', 'DEMO_002', 'DEMO_003'],
        'underlying': ['NIFTY', 'NIFTY', 'BANKNIFTY'],
        'action': ['BUY', 'BUY', 'BUY'],
        'option_type': ['call', 'put', 'call'],
        'strike_price': [22100, 21900, 45200],
        'expiry': [
            (datetime.now() + timedelta(days=15)).isoformat(),
            (datetime.now() + timedelta(days=10)).isoformat(),
            (datetime.now() + timedelta(days=20)).isoformat()
        ],
        'entry_price': [180.0, 120.0, 350.0],
        'lot_size': [2, 3, 1],
        'stoploss': [120.0, 80.0, 250.0],
        'target': [250.0, 180.0, 450.0],
        'confidence_score': [0.78, 0.65, 0.82],
        'timeframe': ['5min', '5min', '15min'],
        'timestamp': [datetime.now().isoformat() for _ in range(3)]
    })
    
    # Save signals file
    signals_file = Path('data/signals/final_consolidated_demo.parquet')
    signals_data.write_parquet(signals_file)
    logger.info(f"✅ Created signals file: {signals_file}")
    
    return signals_file


async def demonstrate_real_data_processing():
    """Demonstrate the agent processing real data"""
    logger.info("🚀 Starting Options Risk Management Agent Demo")
    
    # Create sample data
    await create_sample_market_data()
    signals_file = await create_sample_signals()
    
    # Initialize the agent
    logger.info("🛡️ Initializing Risk Management Agent...")
    agent = OptionsRiskManagementAgent()
    
    try:
        # Initialize agent
        await agent.initialize()
        logger.info("✅ Agent initialized successfully")
        
        # Demonstrate signal processing
        logger.info("\n📨 DEMONSTRATING SIGNAL PROCESSING")
        logger.info("="*50)
        
        # Process the signals file
        await agent.signal_receiver._process_signal_file(signals_file)
        
        # Process signals from queue
        processed_count = 0
        while not agent.pending_signals.empty() and processed_count < 3:
            signal = await agent.pending_signals.get()
            
            logger.info(f"\n📊 Processing Signal: {signal['signal_id']}")
            logger.info(f"   Symbol: {signal['symbol']}")
            logger.info(f"   Option Type: {signal['option_type']}")
            logger.info(f"   Strike: {signal['strike_price']}")
            logger.info(f"   Entry Price: {signal['entry_price']}")
            logger.info(f"   Lot Size: {signal['lot_size']}")
            
            # Enrich with market data
            await agent._enrich_signal_with_market_data(signal)
            
            if 'current_underlying_price' in signal:
                logger.info(f"   Current Underlying Price: {signal['current_underlying_price']:.2f}")
            
            if 'greeks' in signal:
                greeks = signal['greeks']
                logger.info(f"   Greeks: Δ={greeks['delta']:.3f}, Γ={greeks['gamma']:.5f}, Θ={greeks['theta']:.2f}, ν={greeks['vega']:.2f}")
            
            # Evaluate for risk
            decision = await agent.evaluate_signal_for_risk(signal)
            
            logger.info(f"   Risk Decision: {'✅ APPROVED' if decision['approved'] else '❌ REJECTED'}")
            logger.info(f"   Adjusted Lot Size: {decision['adjusted_lot_size']}")
            logger.info(f"   Risk Score: {decision['risk_score']:.2f}")
            logger.info(f"   Reason: {decision['reason']}")
            
            # If approved, simulate trade creation
            if decision['approved'] and decision['adjusted_lot_size'] > 0:
                trade = {
                    'trade_id': f"TRADE_{signal['signal_id']}",
                    'signal_id': signal['signal_id'],
                    'symbol': signal['symbol'],
                    'option_type': signal['option_type'],
                    'strike_price': signal['strike_price'],
                    'entry_price': signal['entry_price'],
                    'lot_size': decision['adjusted_lot_size'],
                    'status': 'open',
                    'greeks': signal['greeks'],
                    'current_price': signal['entry_price']
                }
                
                agent.trade_manager.add_active_trade(trade)
                logger.info(f"   🚀 Trade Created: {trade['trade_id']}")
            
            processed_count += 1
        
        # Demonstrate position monitoring
        logger.info("\n📊 DEMONSTRATING POSITION MONITORING")
        logger.info("="*50)
        
        active_trades = agent.trade_manager.get_active_trades()
        logger.info(f"Active Trades: {len(active_trades)}")
        
        for trade in active_trades:
            logger.info(f"\n🔍 Monitoring Trade: {trade['trade_id']}")
            logger.info(f"   Symbol: {trade['symbol']}")
            logger.info(f"   Entry Price: {trade['entry_price']:.2f}")
            logger.info(f"   Lot Size: {trade['lot_size']}")
            
            # Get current price
            current_price = await agent._get_current_price_from_market_monitoring(trade['symbol'])
            if current_price:
                logger.info(f"   Current Underlying Price: {current_price:.2f}")
                
                # Estimate option price
                estimated_option_price = await agent._estimate_current_option_price(trade, current_price)
                if estimated_option_price:
                    logger.info(f"   Estimated Option Price: {estimated_option_price:.2f}")
                    
                    # Calculate P&L
                    pnl = (estimated_option_price - trade['entry_price']) * trade['lot_size']
                    logger.info(f"   Unrealized P&L: {pnl:.2f}")
        
        # Demonstrate monitoring
        logger.info("\n📈 DEMONSTRATING RISK MONITORING")
        logger.info("="*50)
        
        # Run monitoring cycles
        await agent.monitoring.monitor_portfolio_exposure()
        logger.info(f"Portfolio Exposure: {agent.daily_capital_used:.2f}")
        
        await agent.monitoring.monitor_greeks_exposure()
        logger.info("Greeks monitoring completed")
        
        # Show agent statistics
        logger.info("\n📊 AGENT STATISTICS")
        logger.info("="*50)
        logger.info(f"Total Capital: {agent.total_capital:,.2f}")
        logger.info(f"Current Capital: {agent.current_capital:,.2f}")
        logger.info(f"Daily Capital Used: {agent.daily_capital_used:,.2f}")
        logger.info(f"Active Trades: {len(agent.trade_manager.get_active_trades())}")
        
        # Signal receiver statistics
        stats = agent.signal_receiver.get_statistics()
        logger.info(f"Signals Received: {stats['signals_received']}")
        logger.info(f"Signals Processed: {stats['signals_processed']}")
        
        logger.info("\n🎉 Demo completed successfully!")
        logger.info("The Options Risk Management Agent is now working with real data instead of placeholder/mock data.")
        
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}", exc_info=True)
    
    finally:
        # Cleanup
        await agent.cleanup()
        logger.info("🧹 Agent cleanup completed")


async def main():
    """Main demo function"""
    try:
        await demonstrate_real_data_processing()
    except KeyboardInterrupt:
        logger.info("Demo interrupted by user")
    except Exception as e:
        logger.error(f"Demo failed: {e}", exc_info=True)


if __name__ == '__main__':
    asyncio.run(main())
