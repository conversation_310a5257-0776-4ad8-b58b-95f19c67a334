#!/usr/bin/env python3
"""
Test Runner for Options Risk Management Agent
Runs comprehensive tests to verify all functionality works with real data
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging for tests
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_dependencies():
    """Check if all required dependencies are available"""
    required_packages = [
        'pytest',
        'pytest-asyncio',
        'polars',
        'yaml',
        'scipy'  # Optional for advanced Greeks calculation
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            if package != 'scipy':  # scipy is optional
                missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"Missing required packages: {missing_packages}")
        logger.info("Install missing packages with: pip install " + " ".join(missing_packages))
        return False
    
    return True


def setup_test_environment():
    """Set up the test environment"""
    # Create necessary directories
    test_dirs = [
        'data/live/1min',
        'data/live/3min', 
        'data/live/5min',
        'data/live/15min',
        'data/signals',
        'data/alerts',
        'data/status'
    ]
    
    for dir_path in test_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    logger.info("Test environment set up successfully")


async def run_basic_functionality_test():
    """Run a basic functionality test without pytest"""
    logger.info("Running basic functionality test...")
    
    try:
        # Test imports
        from agents.options_risk_management_agent import OptionsRiskManagementAgent
        from agents.risk_management.signal_receiver import SignalReceiver
        from agents.risk_management.risk_evaluator import RiskEvaluator
        
        logger.info("✅ All imports successful")
        
        # Test agent initialization
        agent = OptionsRiskManagementAgent()
        logger.info("✅ Agent creation successful")
        
        # Test signal processing
        test_signal = {
            'signal_id': 'BASIC_TEST_001',
            'underlying': 'NIFTY',
            'symbol': 'NIFTY',
            'action': 'BUY',
            'option_type': 'call',
            'strike_price': 22000,
            'entry_price': 150.0,
            'lot_size': 2,
            'confidence_score': 0.75
        }
        
        converted_signal = await agent._convert_signal_format(test_signal)
        assert converted_signal['signal_id'] == 'BASIC_TEST_001'
        logger.info("✅ Signal conversion successful")
        
        # Test Greeks calculation
        greeks = await agent._calculate_real_time_greeks(
            underlying_price=22050.0,
            strike_price=22000.0,
            option_type='call',
            time_to_expiry=15,
            implied_volatility=0.18
        )
        
        assert greeks is not None
        assert 'delta' in greeks
        logger.info("✅ Greeks calculation successful")
        
        logger.info("🎉 Basic functionality test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Basic functionality test failed: {e}")
        return False


def run_pytest_tests():
    """Run the full pytest suite"""
    logger.info("Running comprehensive pytest suite...")
    
    try:
        import pytest
        
        # Run tests with verbose output
        test_files = [
            'tests/test_options_risk_management_agent.py',
            'tests/test_agent_communication.py'
        ]
        
        # Check which test files exist
        existing_test_files = [f for f in test_files if Path(f).exists()]
        
        if not existing_test_files:
            logger.warning("No test files found")
            return False
        
        logger.info(f"Running tests: {existing_test_files}")
        
        # Run pytest
        result = pytest.main([
            '-v',  # verbose
            '--tb=short',  # short traceback format
            '--asyncio-mode=auto',  # auto async mode
            *existing_test_files
        ])
        
        if result == 0:
            logger.info("🎉 All pytest tests passed!")
            return True
        else:
            logger.error("❌ Some pytest tests failed")
            return False
            
    except ImportError:
        logger.warning("pytest not available, skipping comprehensive tests")
        return False
    except Exception as e:
        logger.error(f"❌ Error running pytest: {e}")
        return False


async def run_integration_test():
    """Run a simple integration test"""
    logger.info("Running integration test...")
    
    try:
        from agents.options_risk_management_agent import OptionsRiskManagementAgent
        import polars as pl
        import tempfile
        from datetime import datetime, timedelta
        
        # Create temporary config
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            config_content = """
risk_limits:
  max_daily_drawdown_pct: 0.05
  max_capital_at_risk_pct: 0.02
  max_active_trades: 5
  max_daily_capital_usage_pct: 0.20
monitoring_interval: 1
initial_capital: 100000.0
signal_receiver:
  enable_websocket: false
  file_monitor_interval: 1
"""
            f.write(config_content)
            config_file = f.name
        
        # Create agent
        agent = OptionsRiskManagementAgent(config_file)
        await agent.initialize()
        
        # Test signal processing
        test_signal = {
            'signal_id': 'INTEGRATION_001',
            'underlying': 'NIFTY',
            'action': 'BUY',
            'option_type': 'call',
            'strike_price': 22000,
            'entry_price': 150.0,
            'lot_size': 2,
            'stoploss': 100.0,
            'target': 200.0,
            'confidence_score': 0.75,
            'timeframe': '5min'
        }
        
        # Process signal
        await agent._process_received_signal(test_signal)
        
        # Check if signal was queued
        assert not agent.pending_signals.empty()
        
        # Process the signal
        processed_signal = await agent.pending_signals.get()
        
        # Evaluate for risk
        decision = await agent.evaluate_signal_for_risk(processed_signal)
        
        assert 'approved' in decision
        assert 'adjusted_lot_size' in decision
        
        await agent.cleanup()
        
        # Cleanup
        Path(config_file).unlink(missing_ok=True)
        
        logger.info("✅ Integration test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        return False


async def main():
    """Main test runner"""
    logger.info("🚀 Starting Options Risk Management Agent Tests")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Set up test environment
    setup_test_environment()
    
    # Run tests
    test_results = []
    
    # 1. Basic functionality test
    basic_result = await run_basic_functionality_test()
    test_results.append(("Basic Functionality", basic_result))
    
    # 2. Integration test
    integration_result = await run_integration_test()
    test_results.append(("Integration Test", integration_result))
    
    # 3. Comprehensive pytest suite
    pytest_result = run_pytest_tests()
    test_results.append(("Pytest Suite", pytest_result))
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} test suites passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! The Options Risk Management Agent is working correctly with real data.")
        sys.exit(0)
    else:
        logger.error("❌ Some tests failed. Please check the logs above for details.")
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())
