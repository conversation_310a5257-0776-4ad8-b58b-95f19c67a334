#!/usr/bin/env python3
"""
Integration Tests for Agent Communication
Tests communication between Risk Management Agent and Market Monitoring Agent
"""

import pytest
import asyncio
import polars as pl
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

from agents.options_risk_management_agent import OptionsRiskManagementAgent
from agents.options_market_monitoring_agent import OptionsMarketMonitoringAgent


class TestAgentCommunication:
    """Test communication between agents"""
    
    @pytest.fixture
    async def temp_data_dir(self):
        """Create temporary data directory structure"""
        with tempfile.TemporaryDirectory() as temp_dir:
            data_path = Path(temp_dir) / "data"
            live_path = data_path / "live"
            signals_path = data_path / "signals"
            
            # Create directory structure
            for timeframe in ["1min", "3min", "5min", "15min"]:
                (live_path / timeframe).mkdir(parents=True, exist_ok=True)
            
            signals_path.mkdir(parents=True, exist_ok=True)
            
            yield data_path
    
    @pytest.fixture
    async def market_monitoring_agent(self, temp_data_dir):
        """Create a real market monitoring agent with test data"""
        # Create test market data
        test_data = pl.DataFrame({
            'timestamp': [datetime.now() - timedelta(minutes=i) for i in range(10, 0, -1)],
            'open': [22000 + i for i in range(10)],
            'high': [22010 + i for i in range(10)],
            'low': [21990 + i for i in range(10)],
            'close': [22005 + i for i in range(10)],
            'volume': [1000 + i*100 for i in range(10)]
        })
        
        # Save test data files
        for timeframe in ["1min", "5min"]:
            data_file = temp_data_dir / "live" / timeframe / f"Index_NIFTY_latest.parquet"
            test_data.write_parquet(data_file)
        
        # Create config file
        config_data = {
            'underlying_symbols': ['NIFTY', 'BANKNIFTY'],
            'timeframes': ['1min', '3min', '5min', '15min'],
            'monitoring_intervals': {
                '1min': 1,
                '3min': 3,
                '5min': 5,
                '15min': 15
            }
        }
        
        config_file = temp_data_dir / "market_config.yaml"
        with open(config_file, 'w') as f:
            import yaml
            yaml.dump(config_data, f)
        
        # Patch the data path
        with patch.object(OptionsMarketMonitoringAgent, '__init__') as mock_init:
            def init_side_effect(self, config_path=None):
                self.config_path = Path(config_file)
                self.config = config_data
                self.is_running = False
                self.data_path = temp_data_dir
                self.live_path = temp_data_dir / "live"
                self.timeframes = ["1min", "3min", "5min", "15min"]
                self.market_data_cache = {tf: {} for tf in self.timeframes}
                self.last_update = {tf: {} for tf in self.timeframes}
                self.market_regime_state = {tf: {} for tf in self.timeframes}
                self.alerts_cache = []
                self.anomaly_logs = []
                self.strategy_suppression_decisions = []
                
            mock_init.side_effect = init_side_effect
            
            agent = OptionsMarketMonitoringAgent()
            await agent.initialize()
            yield agent
            await agent.cleanup()
    
    @pytest.fixture
    async def risk_management_agent(self, market_monitoring_agent, temp_data_dir):
        """Create risk management agent with real market monitoring agent"""
        # Create config
        config_data = {
            'risk_limits': {
                'max_daily_drawdown_pct': 0.05,
                'max_capital_at_risk_pct': 0.02,
                'max_active_trades': 5,
                'max_daily_capital_usage_pct': 0.20
            },
            'monitoring_interval': 1,
            'initial_capital': 100000.0,
            'signal_receiver': {
                'enable_websocket': False,
                'file_monitor_interval': 1
            }
        }
        
        config_file = temp_data_dir / "risk_config.yaml"
        with open(config_file, 'w') as f:
            import yaml
            yaml.dump(config_data, f)
        
        # Create agent with real market monitoring agent
        with patch('agents.options_risk_management_agent.OptionsMarketMonitoringAgent', return_value=market_monitoring_agent):
            agent = OptionsRiskManagementAgent(str(config_file))
            await agent.initialize()
            yield agent
            await agent.cleanup()
    
    @pytest.mark.asyncio
    async def test_market_data_retrieval(self, market_monitoring_agent):
        """Test retrieving market data from monitoring agent"""
        # Test getting latest market data
        data = market_monitoring_agent.get_latest_market_data('NIFTY', '1min')
        assert data is not None
        assert data.height > 0
        assert 'close' in data.columns
        
        # Test getting current price
        price = market_monitoring_agent.get_current_price('NIFTY', '1min')
        assert price is not None
        assert price > 0
    
    @pytest.mark.asyncio
    async def test_risk_agent_market_data_integration(self, risk_management_agent):
        """Test risk agent integration with market monitoring agent"""
        # Test getting current price through risk agent
        price = await risk_management_agent._get_current_price_from_market_monitoring('NIFTY')
        assert price is not None
        assert price > 0
        
        # Test signal enrichment with market data
        test_signal = {
            'signal_id': 'INTEGRATION_TEST_001',
            'underlying': 'NIFTY',
            'symbol': 'NIFTY',
            'option_type': 'call',
            'strike_price': 22000,
            'entry_price': 150.0,
            'lot_size': 2,
            'time_to_expiry_days': 15,
            'implied_volatility': 0.18
        }
        
        # Enrich signal with market data
        await risk_management_agent._enrich_signal_with_market_data(test_signal)
        
        # Verify enrichment
        assert 'current_underlying_price' in test_signal
        assert test_signal['current_underlying_price'] > 0
        assert 'greeks' in test_signal
        assert test_signal['greeks']['delta'] != 0
    
    @pytest.mark.asyncio
    async def test_end_to_end_signal_processing(self, risk_management_agent, temp_data_dir):
        """Test end-to-end signal processing from file to trade execution"""
        # Create a signal file
        signals_path = temp_data_dir / "signals"
        
        test_signals = pl.DataFrame({
            'signal_id': ['E2E_TEST_001'],
            'underlying': ['NIFTY'],
            'action': ['BUY'],
            'option_type': ['call'],
            'strike_price': [22000],
            'expiry': [(datetime.now() + timedelta(days=15)).isoformat()],
            'entry_price': [150.0],
            'lot_size': [2],
            'stoploss': [100.0],
            'target': [200.0],
            'confidence_score': [0.75],
            'timeframe': ['5min'],
            'timestamp': [datetime.now().isoformat()]
        })
        
        signal_file = signals_path / "final_consolidated_e2e_test.parquet"
        test_signals.write_parquet(signal_file)
        
        # Process the signal file
        await risk_management_agent.signal_receiver._process_signal_file(signal_file)
        
        # Wait a bit for processing
        await asyncio.sleep(0.1)
        
        # Check if signal was processed
        assert not risk_management_agent.pending_signals.empty()
        
        # Process one signal from the queue
        signal = await risk_management_agent.pending_signals.get()
        
        # Enrich with market data
        await risk_management_agent._enrich_signal_with_market_data(signal)
        
        # Evaluate for risk
        decision = await risk_management_agent.evaluate_signal_for_risk(signal)
        
        # Verify the decision
        assert 'approved' in decision
        assert 'adjusted_lot_size' in decision
        
        # If approved, verify trade creation logic would work
        if decision['approved'] and decision['adjusted_lot_size'] > 0:
            new_trade = {
                "trade_id": f"TRADE_{signal['signal_id']}",
                "signal_id": signal['signal_id'],
                "symbol": signal['symbol'],
                "underlying": signal.get('underlying', signal['symbol']),
                "option_type": signal['option_type'],
                "strike_price": signal['strike_price'],
                "entry_price": signal['entry_price'],
                "lot_size": decision['adjusted_lot_size'],
                "status": "open",
                "timestamp": datetime.now().isoformat(),
                "greeks": signal['greeks'],
                "current_price": signal['entry_price']
            }
            
            # Verify trade structure
            assert new_trade['trade_id'].startswith('TRADE_')
            assert new_trade['lot_size'] > 0
            assert new_trade['greeks']['delta'] != 0
    
    @pytest.mark.asyncio
    async def test_position_monitoring_with_real_market_data(self, risk_management_agent):
        """Test position monitoring using real market data"""
        # Add a test trade
        test_trade = {
            'trade_id': 'MONITOR_INTEGRATION_001',
            'symbol': 'NIFTY',
            'underlying': 'NIFTY',
            'option_type': 'call',
            'strike_price': 22000,
            'entry_price': 150.0,
            'lot_size': 2,
            'status': 'open',
            'greeks': {'delta': 0.6, 'gamma': 0.002, 'theta': -8.5, 'vega': 45.2},
            'entry_underlying_price': 22000.0,
            'time_to_expiry_days': 15,
            'implied_volatility': 0.18
        }
        
        risk_management_agent.trade_manager.add_active_trade(test_trade)
        
        # Set exit parameters
        risk_management_agent.trade_manager.set_trade_exit_parameters(
            trade_id='MONITOR_INTEGRATION_001',
            stop_loss_price=100.0,
            take_profit_price=200.0,
            trailing_stop_pct=0.15
        )
        
        # Get current market price
        current_underlying_price = await risk_management_agent._get_current_price_from_market_monitoring('NIFTY')
        assert current_underlying_price is not None
        
        # Test option price estimation with real underlying price
        estimated_option_price = await risk_management_agent._estimate_current_option_price(
            test_trade, current_underlying_price
        )
        assert estimated_option_price is not None
        assert estimated_option_price > 0
        
        # Test Greeks update with real market data
        await risk_management_agent._update_trade_greeks(test_trade, current_underlying_price)
        
        # Verify Greeks were updated
        assert test_trade['greeks']['delta'] != 0
        assert test_trade['greeks']['gamma'] != 0
    
    @pytest.mark.asyncio
    async def test_monitoring_alerts_with_real_data(self, risk_management_agent):
        """Test monitoring alerts using real market data"""
        # Add multiple trades to create exposure
        test_trades = [
            {
                'trade_id': f'ALERT_TEST_{i}',
                'symbol': 'NIFTY',
                'underlying': 'NIFTY',
                'entry_price': 150.0,
                'lot_size': 5,  # Large lot size to trigger alerts
                'current_price': 160.0,
                'greeks': {'delta': 0.8, 'gamma': 0.005, 'theta': -10.0, 'vega': 50.0}
            }
            for i in range(3)
        ]
        
        for trade in test_trades:
            risk_management_agent.trade_manager.add_active_trade(trade)
        
        # Capture alerts
        alerts_sent = []
        
        async def mock_send_alert(title, message):
            alerts_sent.append({'title': title, 'message': message})
        
        risk_management_agent.monitoring._send_alert = mock_send_alert
        
        # Run monitoring cycles
        await risk_management_agent.monitoring.monitor_portfolio_exposure()
        await risk_management_agent.monitoring.monitor_greeks_exposure()
        
        # Verify exposure calculations
        assert risk_management_agent.daily_capital_used > 0
        
        # Check if any alerts were triggered (depends on exposure levels)
        # This is more of a smoke test to ensure monitoring runs without errors


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
