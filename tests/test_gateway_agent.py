#!/usr/bin/env python3
"""
Test Suite for Gateway-based Performance Analysis Agent

This test demonstrates how the gateway pattern works and validates
that the agent maintains all functionality while being much smaller.
"""

import asyncio
import pytest
import polars as pl
from datetime import datetime, timedelta
from pathlib import Path
import tempfile
import yaml

# Import the gateway-based agent
from agents.performance_analysis.gateway_agent import PerformanceAnalysisGateway, QueryType

class TestGatewayAgent:
    """Test the gateway-based agent"""
    
    @pytest.fixture
    def temp_config_file(self):
        """Create a temporary config file for testing"""
        config_data = {
            'data_paths': {
                'trades_data': 'test_trades.parquet',
                'historical_data': 'test_historical.parquet',
                'greeks_data': 'test_greeks.parquet',
                'export_path': 'test_exports'
            },
            'analysis_settings': {
                'analysis_interval': 300,
                'risk_free_rate': 0.06,
                'min_trades_for_analysis': 10,
                'monte_carlo_simulations': 1000,
                'confidence_levels': [0.95, 0.99]
            },
            'notification_settings': {
                'enable_windows_notifications': False,
                'enable_email_alerts': False,
                'alert_thresholds': {
                    'max_drawdown': 0.15,
                    'daily_loss': 0.05,
                    'win_rate_drop': 0.10,
                    'sharpe_drop': 0.5
                }
            },
            'performance': {
                'max_workers': 2,
                'enable_lazy_loading': True,
                'cache_size': 100,
                'batch_size': 100
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            return Path(f.name)
    
    @pytest.mark.asyncio
    async def test_gateway_initialization(self, temp_config_file):
        """Test gateway initialization"""
        gateway = PerformanceAnalysisGateway(temp_config_file)
        
        # Test initialization
        success = await gateway.initialize()
        assert success, "Gateway should initialize successfully"
        
        # Test health check
        health = await gateway.process_query(QueryType.HEALTH_CHECK)
        assert health["status"] == "healthy"
        assert health["config_loaded"] == True
        
        # Cleanup
        await gateway.cleanup()
        temp_config_file.unlink()
    
    @pytest.mark.asyncio
    async def test_query_routing(self, temp_config_file):
        """Test that queries are routed to correct modules"""
        gateway = PerformanceAnalysisGateway(temp_config_file)
        await gateway.initialize()
        
        try:
            # Test different query types
            query_types = [
                QueryType.HEALTH_CHECK,
                QueryType.NATURAL_LANGUAGE,
            ]
            
            for query_type in query_types:
                result = await gateway.process_query(query_type)
                
                # All queries should return a result with metadata
                assert "_metadata" in result
                assert result["_metadata"]["query_type"] == query_type.value
                assert result["_metadata"]["status"] in ["success", "error"]
                
        finally:
            await gateway.cleanup()
            temp_config_file.unlink()
    
    @pytest.mark.asyncio
    async def test_natural_language_interface(self, temp_config_file):
        """Test natural language query interface"""
        gateway = PerformanceAnalysisGateway(temp_config_file)
        await gateway.initialize()
        
        try:
            # Test various natural language queries
            queries = [
                "What is my win rate?",
                "How much profit have I made?",
                "What is my risk level?",
                "How are my strategies performing?",
                "Random question that doesn't match patterns"
            ]
            
            for query in queries:
                result = await gateway.process_query(
                    QueryType.NATURAL_LANGUAGE, 
                    {"query": query}
                )
                
                # Should always return an answer
                assert "answer" in result
                assert isinstance(result["answer"], str)
                assert len(result["answer"]) > 0
                
        finally:
            await gateway.cleanup()
            temp_config_file.unlink()
    
    @pytest.mark.asyncio
    async def test_convenience_methods(self, temp_config_file):
        """Test convenience methods"""
        gateway = PerformanceAnalysisGateway(temp_config_file)
        await gateway.initialize()
        
        try:
            # Test convenience methods
            win_rate = await gateway.get_win_rate()
            assert isinstance(win_rate, (int, float))
            
            total_pnl = await gateway.get_total_pnl()
            assert isinstance(total_pnl, (int, float))
            
            risk_level = await gateway.get_risk_level()
            assert isinstance(risk_level, str)
            
            # Test natural language interface
            answer = await gateway.ask("What is my win rate?")
            assert isinstance(answer, str)
            assert len(answer) > 0
            
        finally:
            await gateway.cleanup()
            temp_config_file.unlink()
    
    @pytest.mark.asyncio
    async def test_lazy_loading(self, temp_config_file):
        """Test that modules are loaded lazily"""
        gateway = PerformanceAnalysisGateway(temp_config_file)
        await gateway.initialize()
        
        try:
            # Initially no modules should be loaded
            assert len(gateway._modules) == 0
            
            # After a query, the required module should be loaded
            await gateway.process_query(QueryType.HEALTH_CHECK)
            
            # Health check doesn't require any analysis modules
            # So modules should still be empty
            assert len(gateway._modules) == 0
            
        finally:
            await gateway.cleanup()
            temp_config_file.unlink()

@pytest.mark.asyncio
async def test_gateway_performance():
    """Test that gateway pattern provides performance benefits"""
    import time
    
    # Test initialization time
    start_time = time.time()
    gateway = PerformanceAnalysisGateway()
    await gateway.initialize()
    init_time = time.time() - start_time
    
    # Initialization should be fast (< 1 second)
    assert init_time < 1.0, f"Initialization took {init_time:.2f}s, should be < 1s"
    
    # Test query processing time
    start_time = time.time()
    result = await gateway.process_query(QueryType.HEALTH_CHECK)
    query_time = time.time() - start_time
    
    # Query should be fast (< 0.1 seconds)
    assert query_time < 0.1, f"Query took {query_time:.2f}s, should be < 0.1s"
    
    await gateway.cleanup()

def test_gateway_size():
    """Test that gateway agent is significantly smaller than original"""
    gateway_file = Path("agents/performance_analysis/gateway_agent.py")
    
    if gateway_file.exists():
        with open(gateway_file, 'r') as f:
            gateway_lines = len(f.readlines())
        
        # Gateway should be much smaller than original (< 600 lines)
        assert gateway_lines < 600, f"Gateway has {gateway_lines} lines, should be < 600"
        print(f"✅ Gateway agent: {gateway_lines} lines (vs original 1593 lines)")

async def demo_gateway_usage():
    """Demonstrate gateway usage"""
    print("🚀 Gateway Agent Demo")
    print("=" * 40)
    
    gateway = PerformanceAnalysisGateway()
    
    try:
        # Initialize
        print("📋 Initializing gateway...")
        await gateway.initialize()
        
        # Health check
        print("🏥 Health check...")
        health = await gateway.process_query(QueryType.HEALTH_CHECK)
        print(f"   Status: {health['status']}")
        
        # Natural language queries
        print("💬 Natural language queries...")
        questions = [
            "What is my win rate?",
            "How much money have I made?",
            "What is my risk level?"
        ]
        
        for question in questions:
            answer = await gateway.ask(question)
            print(f"   Q: {question}")
            print(f"   A: {answer}")
        
        # Export data
        print("📄 Exporting data...")
        export_result = await gateway.process_query(
            QueryType.EXPORT_DATA,
            {"type": "portfolio_summary", "format": "json"}
        )
        print(f"   Exported: {export_result.get('exported_file', 'N/A')}")
        
        print("✅ Demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
    finally:
        await gateway.cleanup()

if __name__ == "__main__":
    # Run the demo
    print("Running Gateway Agent Demo...")
    asyncio.run(demo_gateway_usage())
    
    # Run tests
    print("\nRunning Tests...")
    pytest.main([__file__, "-v"])
