#!/usr/bin/env python3
"""
Comprehensive Unit Tests for Options Risk Management Agent
Tests all modules with real data and communication between agents
"""

import pytest
import asyncio
import json
import tempfile
import polars as pl
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch, MagicMock

# Import the modules to test
from agents.options_risk_management_agent import OptionsRiskManagementAgent
from agents.risk_management.signal_receiver import SignalReceiver
from agents.risk_management.risk_evaluator import RiskEvaluator
from agents.risk_management.monitoring import Monitoring
from agents.risk_management.trade_manager import TradeManager
from agents.options_market_monitoring_agent import OptionsMarketMonitoringAgent


class TestOptionsRiskManagementAgent:
    """Test suite for the Options Risk Management Agent"""
    
    @pytest.fixture
    async def temp_config_file(self):
        """Create a temporary configuration file for testing"""
        config_data = {
            'risk_limits': {
                'max_daily_drawdown_pct': 0.05,
                'max_capital_at_risk_pct': 0.02,
                'max_active_trades': 5,
                'max_daily_capital_usage_pct': 0.20,
                'max_delta_exposure_pct': 0.10,
                'max_gamma_exposure_pct': 0.05,
                'max_theta_exposure_pct': 0.02,
                'max_vega_exposure_pct': 0.08,
                'greeks_alert_threshold_pct': 0.8
            },
            'monitoring_interval': 1,  # Fast for testing
            'initial_capital': 100000.0,
            'signal_receiver': {
                'enable_websocket': False,
                'file_monitor_interval': 1
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            import yaml
            yaml.dump(config_data, f)
            yield f.name
        
        # Cleanup
        Path(f.name).unlink(missing_ok=True)
    
    @pytest.fixture
    async def mock_market_monitoring_agent(self):
        """Create a mock market monitoring agent"""
        mock_agent = Mock(spec=OptionsMarketMonitoringAgent)
        mock_agent.initialize = AsyncMock(return_value=True)
        mock_agent.get_current_price = Mock(return_value=450.0)
        mock_agent.get_latest_market_data = Mock(return_value=pl.DataFrame({
            'timestamp': [datetime.now()],
            'open': [449.0],
            'high': [451.0],
            'low': [448.0],
            'close': [450.0],
            'volume': [1000]
        }))
        mock_agent.get_market_regime_data = Mock(return_value={
            'trend': 'bullish',
            'volatility': 'normal',
            'regime': 'trending'
        })
        return mock_agent
    
    @pytest.fixture
    async def risk_agent(self, temp_config_file, mock_market_monitoring_agent):
        """Create a risk management agent for testing"""
        with patch('agents.options_risk_management_agent.OptionsMarketMonitoringAgent', return_value=mock_market_monitoring_agent):
            agent = OptionsRiskManagementAgent(temp_config_file)
            await agent.initialize()
            yield agent
            await agent.cleanup()
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, risk_agent):
        """Test that the agent initializes correctly"""
        assert risk_agent.state_manager.is_running()
        assert risk_agent.total_capital == 100000.0
        assert risk_agent.current_capital == 100000.0
        assert risk_agent.agent_id.startswith("RiskAgent_")
        assert risk_agent.signal_receiver is not None
        assert risk_agent.market_monitoring_agent is not None
    
    @pytest.mark.asyncio
    async def test_real_signal_processing(self, risk_agent):
        """Test processing of real signals from signal generation agent"""
        # Create a realistic signal
        test_signal = {
            'signal_id': 'TEST_SIG_001',
            'underlying': 'NIFTY',
            'symbol': 'NIFTY',
            'action': 'BUY',
            'option_type': 'call',
            'strike_price': 22000,
            'expiry': (datetime.now() + timedelta(days=15)).isoformat(),
            'entry_price': 150.0,
            'lot_size': 2,
            'stoploss': 100.0,
            'target': 200.0,
            'confidence_score': 0.75,
            'timeframe': '5min',
            'timestamp': datetime.now().isoformat()
        }
        
        # Process the signal
        await risk_agent._process_received_signal(test_signal)
        
        # Check that signal was queued
        assert not risk_agent.pending_signals.empty()
        
        # Process the queued signal
        processed_signal = await risk_agent.pending_signals.get()
        
        # Verify signal conversion
        assert processed_signal['signal_id'] == 'TEST_SIG_001'
        assert processed_signal['symbol'] == 'NIFTY'
        assert processed_signal['option_type'] == 'call'
        assert processed_signal['strike_price'] == 22000
        assert processed_signal['entry_price'] == 150.0
        assert processed_signal['lot_size'] == 2
    
    @pytest.mark.asyncio
    async def test_risk_evaluation_with_real_data(self, risk_agent):
        """Test risk evaluation using real market data"""
        # Create a signal with real market data
        signal = {
            'signal_id': 'RISK_TEST_001',
            'symbol': 'NIFTY',
            'option_type': 'call',
            'strike_price': 22000,
            'entry_price': 150.0,
            'lot_size': 2,
            'current_underlying_price': 22050.0,
            'implied_volatility': 0.18,
            'time_to_expiry_days': 15,
            'greeks': {
                'delta': 0.6,
                'gamma': 0.002,
                'theta': -8.5,
                'vega': 45.2
            }
        }
        
        # Evaluate the signal
        decision = await risk_agent.evaluate_signal_for_risk(signal)
        
        # Verify decision structure
        assert 'approved' in decision
        assert 'risk_score' in decision
        assert 'adjusted_lot_size' in decision
        assert 'reason' in decision
        assert 'llm_explanation' in decision
        
        # For a reasonable signal, it should be approved
        assert decision['approved'] is True
        assert decision['adjusted_lot_size'] > 0
    
    @pytest.mark.asyncio
    async def test_greeks_calculation(self, risk_agent):
        """Test real-time Greeks calculation"""
        # Test Greeks calculation with realistic parameters
        greeks = await risk_agent._calculate_real_time_greeks(
            underlying_price=22050.0,
            strike_price=22000.0,
            option_type='call',
            time_to_expiry=15,
            implied_volatility=0.18
        )
        
        assert greeks is not None
        assert 'delta' in greeks
        assert 'gamma' in greeks
        assert 'theta' in greeks
        assert 'vega' in greeks
        
        # For an ITM call, delta should be positive and > 0.5
        assert greeks['delta'] > 0.5
        assert greeks['gamma'] > 0
        assert greeks['theta'] < 0  # Time decay
        assert greeks['vega'] > 0
    
    @pytest.mark.asyncio
    async def test_position_monitoring_with_live_prices(self, risk_agent):
        """Test position monitoring with live price updates"""
        # Add a test trade
        test_trade = {
            'trade_id': 'MONITOR_TEST_001',
            'symbol': 'NIFTY',
            'underlying': 'NIFTY',
            'option_type': 'call',
            'strike_price': 22000,
            'entry_price': 150.0,
            'lot_size': 2,
            'status': 'open',
            'greeks': {'delta': 0.6, 'gamma': 0.002, 'theta': -8.5, 'vega': 45.2},
            'entry_underlying_price': 22000.0,
            'time_to_expiry_days': 15
        }
        
        risk_agent.trade_manager.add_active_trade(test_trade)
        
        # Set stop loss and take profit
        risk_agent.trade_manager.set_trade_exit_parameters(
            trade_id='MONITOR_TEST_001',
            stop_loss_price=100.0,
            take_profit_price=200.0,
            trailing_stop_pct=0.15
        )
        
        # Test price estimation
        current_price = await risk_agent._estimate_current_option_price(
            test_trade, 22100.0  # Underlying moved up
        )
        
        assert current_price is not None
        assert current_price > test_trade['entry_price']  # Should be higher due to underlying increase
    
    @pytest.mark.asyncio
    async def test_signal_receiver_file_monitoring(self):
        """Test signal receiver file monitoring functionality"""
        # Create temporary signals directory
        with tempfile.TemporaryDirectory() as temp_dir:
            signals_path = Path(temp_dir) / "signals"
            signals_path.mkdir()
            
            # Mock callback
            callback = AsyncMock()
            
            # Create signal receiver
            config = {
                'enable_websocket': False,
                'file_monitor_interval': 0.1
            }
            
            receiver = SignalReceiver(config, callback)
            receiver.signals_path = signals_path
            
            # Create a test signal file
            test_signals = pl.DataFrame({
                'signal_id': ['TEST_001', 'TEST_002'],
                'underlying': ['NIFTY', 'BANKNIFTY'],
                'action': ['BUY', 'BUY'],
                'option_type': ['call', 'put'],
                'strike_price': [22000, 45000],
                'entry_price': [150.0, 300.0],
                'lot_size': [2, 1],
                'confidence_score': [0.75, 0.68]
            })
            
            signal_file = signals_path / "final_consolidated_test.parquet"
            test_signals.write_parquet(signal_file)
            
            # Process the file
            await receiver._process_signal_file(signal_file)
            
            # Verify callback was called for each signal
            assert callback.call_count == 2
    
    @pytest.mark.asyncio
    async def test_monitoring_with_dynamic_thresholds(self, risk_agent):
        """Test monitoring with dynamic thresholds based on portfolio size"""
        # Add some test trades to create exposure
        test_trades = [
            {
                'trade_id': f'EXPOSURE_TEST_{i}',
                'symbol': 'NIFTY',
                'entry_price': 150.0,
                'lot_size': 2,
                'current_price': 160.0,
                'greeks': {'delta': 0.6, 'gamma': 0.002, 'theta': -8.5, 'vega': 45.2}
            }
            for i in range(3)
        ]
        
        for trade in test_trades:
            risk_agent.trade_manager.add_active_trade(trade)
        
        # Test portfolio exposure monitoring
        monitoring = risk_agent.monitoring
        
        # Mock the alert sending to capture alerts
        alerts_sent = []
        
        async def mock_send_alert(title, message):
            alerts_sent.append({'title': title, 'message': message})
        
        monitoring._send_alert = mock_send_alert
        
        # Run one cycle of portfolio monitoring
        await monitoring.monitor_portfolio_exposure()
        
        # Verify exposure was calculated
        assert risk_agent.daily_capital_used > 0
        
        # Test Greeks monitoring
        await monitoring.monitor_greeks_exposure()
        
        # Should have calculated total Greeks exposure
        # (specific values depend on the test trades)


class TestSignalReceiver:
    """Test suite for Signal Receiver"""
    
    @pytest.mark.asyncio
    async def test_signal_validation(self):
        """Test signal validation logic"""
        callback = AsyncMock()
        config = {'enable_websocket': False}
        receiver = SignalReceiver(config, callback)
        
        # Valid signal
        valid_signal = {
            'signal_id': 'VALID_001',
            'underlying': 'NIFTY',
            'action': 'BUY',
            'option_type': 'call'
        }
        assert receiver._validate_signal(valid_signal) is True
        
        # Invalid signal - missing required field
        invalid_signal = {
            'signal_id': 'INVALID_001',
            'underlying': 'NIFTY'
            # Missing action and option_type
        }
        assert receiver._validate_signal(invalid_signal) is False
        
        # Invalid signal - wrong action
        invalid_action_signal = {
            'signal_id': 'INVALID_002',
            'underlying': 'NIFTY',
            'action': 'INVALID_ACTION',
            'option_type': 'call'
        }
        assert receiver._validate_signal(invalid_action_signal) is False


class TestRiskEvaluator:
    """Test suite for Risk Evaluator"""
    
    @pytest.fixture
    def mock_config(self):
        return {
            'risk_limits': {
                'max_capital_at_risk_pct': 0.02,
                'max_daily_drawdown_pct': 0.05,
                'max_active_trades': 5
            }
        }
    
    @pytest.fixture
    def mock_agent(self):
        agent = Mock()
        agent.current_capital = 100000.0
        agent.daily_high_capital = 100000.0
        agent.trade_manager = Mock()
        agent.trade_manager.get_active_trades.return_value = []
        agent.trade_manager.calculate_position_size.return_value = 2
        return agent
    
    def test_advanced_risk_metrics_calculation(self, mock_config, mock_agent):
        """Test advanced risk metrics calculation"""
        evaluator = RiskEvaluator(mock_config, mock_agent)
        
        metrics = {
            'current_underlying_price': 22050.0,
            'strike_price': 22000.0,
            'option_type': 'call',
            'entry_price': 150.0,
            'time_to_expiry_days': 15,
            'theta': -8.5,
            'vega': 45.2
        }
        
        advanced_metrics = evaluator._calculate_advanced_risk_metrics(metrics)
        
        assert 'moneyness' in advanced_metrics
        assert 'probability_itm' in advanced_metrics
        assert 'max_loss_per_contract' in advanced_metrics
        assert 'breakeven_price' in advanced_metrics
        assert 'time_decay_risk' in advanced_metrics
        assert 'volatility_risk' in advanced_metrics
        
        # For ITM call, probability should be > 0.5
        assert advanced_metrics['probability_itm'] > 0.5
        assert advanced_metrics['moneyness'] > 1.0  # ITM
        assert advanced_metrics['max_loss_per_contract'] == 150.0


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
