#!/usr/bin/env python3
"""
Test Suite for Performance Analysis Modules

This test suite verifies that all modular components work correctly
and that the refactored agent maintains all functionality.
"""

import asyncio
import pytest
import polars as pl
from datetime import datetime, timedelta
from pathlib import Path
import tempfile
import yaml

# Import modules to test
from agents.performance_analysis.config_manager import OptionsPerformanceConfig
from agents.performance_analysis.trade_evaluator import TradeEvaluator
from agents.performance_analysis.strategy_aggregator import StrategyAggregator
from agents.performance_analysis.model_monitoring import ModelPerformanceMonitor
from agents.performance_analysis.regime_analyzer import RegimeAnalyzer
from agents.performance_analysis.risk_feedback import RiskFeedbackAnalyzer

class TestConfigManager:
    """Test configuration management module"""
    
    @pytest.fixture
    def temp_config_file(self):
        """Create a temporary config file for testing"""
        config_data = {
            'data_paths': {
                'trades_data': 'test_trades.parquet',
                'historical_data': 'test_historical.parquet',
                'greeks_data': 'test_greeks.parquet',
                'export_path': 'test_exports'
            },
            'analysis_settings': {
                'analysis_interval': 300,
                'risk_free_rate': 0.06,
                'min_trades_for_analysis': 10,
                'monte_carlo_simulations': 1000,
                'confidence_levels': [0.95, 0.99]
            },
            'notification_settings': {
                'enable_windows_notifications': False,
                'enable_email_alerts': False,
                'alert_thresholds': {
                    'max_drawdown': 0.15,
                    'daily_loss': 0.05,
                    'win_rate_drop': 0.10,
                    'sharpe_drop': 0.5
                }
            },
            'performance': {
                'max_workers': 2,
                'enable_lazy_loading': True,
                'cache_size': 100,
                'batch_size': 100
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            return Path(f.name)
    
    @pytest.mark.asyncio
    async def test_config_loading(self, temp_config_file):
        """Test configuration loading and validation"""
        config_manager = OptionsPerformanceConfig(temp_config_file)
        
        # Test loading
        success = await config_manager.load_config()
        assert success, "Configuration should load successfully"
        
        # Test validation
        is_valid = config_manager.validate_config()
        assert is_valid, "Configuration should be valid"
        
        # Test configuration access
        assert config_manager.data_paths.trades_data == 'test_trades.parquet'
        assert config_manager.analysis_settings.risk_free_rate == 0.06
        assert config_manager.performance_config.max_workers == 2
        
        # Cleanup
        temp_config_file.unlink()

class TestTradeEvaluator:
    """Test trade evaluation module"""
    
    @pytest.fixture
    def sample_trades_df(self):
        """Create sample trade data for testing"""
        return pl.DataFrame({
            'trade_id': ['trade_1', 'trade_2', 'trade_3'],
            'strategy_id': ['strat_001', 'strat_002', 'strat_001'],
            'entry_time': [datetime.now() - timedelta(hours=2), 
                          datetime.now() - timedelta(hours=1),
                          datetime.now()],
            'exit_time': [datetime.now() - timedelta(hours=1),
                         datetime.now() - timedelta(minutes=30),
                         datetime.now() + timedelta(minutes=30)],
            'entry_price': [100.0, 150.0, 120.0],
            'exit_price': [110.0, 140.0, 125.0],
            'quantity': [10, 5, 8],
            'trade_type': ['CE', 'PE', 'CE'],
            'expected_entry_price': [99.0, 151.0, 119.0],
            'expected_exit_price': [111.0, 139.0, 126.0],
            'signal_confidence': [0.8, 0.6, 0.9],
            'is_target_hit': [True, False, True],
            'is_sl_hit': [False, True, False],
            'is_manual_exit': [False, False, False],
            'market_regime': ['bullish', 'bearish', 'sideways'],
            'volatility_regime': ['medium_vol', 'high_vol', 'low_vol'],
            'news_day': [False, True, False],
            'expiry_proximity': ['medium', 'near', 'far'],
            'model_predicted_direction': ['long', 'short', 'long'],
            'model_predicted_roi': [8.0, -5.0, 4.0],
            'actual_direction': ['long', 'short', 'long'],
            'allowed_capital_at_risk': [1000.0, 750.0, 960.0],
            'actual_loss': [0.0, -50.0, 0.0],
            'daily_drawdown_threshold': [500.0, 500.0, 500.0],
            'trading_paused': [False, False, False],
            'risky_signals_filtered': [False, True, False],
            'signal_source': ['model_a', 'model_b', 'model_a'],
            'strike_price': [18000, 17500, 18200],
            'option_type': ['CALL', 'PUT', 'CALL'],
            'underlying_entry_price': [17950.0, 17600.0, 18150.0],
            'underlying_exit_price': [18050.0, 17550.0, 18200.0]
        })
    
    @pytest.mark.asyncio
    async def test_trade_evaluation(self, sample_trades_df):
        """Test trade-level performance evaluation"""
        evaluator = TradeEvaluator()
        
        # Create temporary paths for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            historical_path = Path(temp_dir) / "historical.parquet"
            greeks_path = Path(temp_dir) / "greeks.parquet"
            
            # Create empty files
            historical_path.touch()
            greeks_path.touch()
            
            # Test evaluation
            evaluated_df = await evaluator.evaluate_trade_level_performance(
                sample_trades_df, historical_path, greeks_path
            )
            
            # Verify new columns are added
            expected_columns = ['roi_percent', 'absolute_pnl', 'pnl_category', 
                              'slippage_reason', 'was_signal_accurate', 'execution_score']
            
            for col in expected_columns:
                assert col in evaluated_df.columns, f"Column {col} should be present"
            
            # Verify calculations
            assert evaluated_df.height == 3, "Should have 3 evaluated trades"
            
            # Test statistics calculation
            stats = await evaluator.calculate_trade_statistics(evaluated_df)
            assert 'total_trades' in stats
            assert 'win_rate' in stats
            assert stats['total_trades'] == 3

class TestStrategyAggregator:
    """Test strategy aggregation module"""
    
    @pytest.fixture
    def evaluated_trades_df(self):
        """Create sample evaluated trade data"""
        return pl.DataFrame({
            'strategy_id': ['strat_001', 'strat_001', 'strat_002', 'strat_002'],
            'entry_time': [datetime.now() - timedelta(days=1),
                          datetime.now() - timedelta(hours=12),
                          datetime.now() - timedelta(hours=6),
                          datetime.now()],
            'roi_percent': [5.0, -2.0, 8.0, 3.0],
            'absolute_pnl': [500.0, -200.0, 800.0, 300.0],
            'pnl_category': ['Profit', 'Loss', 'Profit', 'Profit'],
            'allowed_capital_at_risk': [1000.0, 1000.0, 1000.0, 1000.0],
            'signal_confidence': [0.8, 0.6, 0.9, 0.7],
            'execution_score': [85, 70, 90, 80],
            'holding_time_minutes': [60, 45, 90, 30],
            'was_signal_accurate': [True, False, True, True]
        })
    
    @pytest.mark.asyncio
    async def test_strategy_aggregation(self, evaluated_trades_df):
        """Test strategy metrics aggregation"""
        aggregator = StrategyAggregator()
        
        # Test daily aggregation
        daily_metrics = await aggregator.aggregate_strategy_metrics(
            evaluated_trades_df, period="daily"
        )
        
        assert isinstance(daily_metrics, dict), "Should return dictionary"
        assert len(daily_metrics) > 0, "Should have strategy metrics"
        
        # Test portfolio metrics
        portfolio_metrics = await aggregator.calculate_portfolio_metrics(evaluated_trades_df)
        
        assert 'total_trades' in portfolio_metrics
        assert 'win_rate' in portfolio_metrics
        assert portfolio_metrics['total_trades'] == 4

class TestModelMonitor:
    """Test model performance monitoring module"""
    
    @pytest.fixture
    def sample_predictions_df(self):
        """Create sample model predictions"""
        return pl.DataFrame({
            'trade_id': ['trade_1', 'trade_2', 'trade_3'],
            'predicted_direction': ['long', 'short', 'long'],
            'predicted_roi': [5.0, -3.0, 4.0],
            'prediction_confidence': [0.8, 0.7, 0.9]
        })
    
    @pytest.fixture
    def sample_actual_df(self):
        """Create sample actual outcomes"""
        return pl.DataFrame({
            'trade_id': ['trade_1', 'trade_2', 'trade_3'],
            'actual_direction': ['long', 'short', 'short'],
            'roi_percent': [6.0, -2.5, -1.0]
        })
    
    @pytest.mark.asyncio
    async def test_model_monitoring(self, sample_predictions_df, sample_actual_df):
        """Test model performance monitoring"""
        monitor = ModelPerformanceMonitor()
        await monitor.initialize()
        
        # Test monitoring
        performance_metrics = await monitor.monitor_model_performance(
            sample_predictions_df, sample_actual_df
        )
        
        assert 'direction_accuracy' in performance_metrics
        assert 'roi_prediction_mae' in performance_metrics
        assert 'recommendations' in performance_metrics

@pytest.mark.asyncio
async def test_integration():
    """Test integration between modules"""
    # This test verifies that modules can work together
    
    # Create sample data
    trades_df = pl.DataFrame({
        'trade_id': ['trade_1'],
        'strategy_id': ['strat_001'],
        'entry_time': [datetime.now()],
        'exit_time': [datetime.now() + timedelta(hours=1)],
        'entry_price': [100.0],
        'exit_price': [105.0],
        'quantity': [10],
        'trade_type': ['CE'],
        'expected_entry_price': [99.0],
        'expected_exit_price': [106.0],
        'signal_confidence': [0.8],
        'is_target_hit': [True],
        'is_sl_hit': [False],
        'is_manual_exit': [False],
        'market_regime': ['bullish'],
        'volatility_regime': ['medium_vol'],
        'news_day': [False],
        'expiry_proximity': ['medium'],
        'model_predicted_direction': ['long'],
        'model_predicted_roi': [5.0],
        'actual_direction': ['long'],
        'allowed_capital_at_risk': [1000.0],
        'actual_loss': [0.0],
        'daily_drawdown_threshold': [500.0],
        'trading_paused': [False],
        'risky_signals_filtered': [False],
        'signal_source': ['model_a'],
        'strike_price': [18000],
        'option_type': ['CALL'],
        'underlying_entry_price': [17950.0],
        'underlying_exit_price': [18050.0]
    })
    
    # Test module integration
    evaluator = TradeEvaluator()
    aggregator = StrategyAggregator()
    
    with tempfile.TemporaryDirectory() as temp_dir:
        historical_path = Path(temp_dir) / "historical.parquet"
        greeks_path = Path(temp_dir) / "greeks.parquet"
        historical_path.touch()
        greeks_path.touch()
        
        # Evaluate trades
        evaluated_trades = await evaluator.evaluate_trade_level_performance(
            trades_df, historical_path, greeks_path
        )
        
        # Aggregate strategies
        strategy_metrics = await aggregator.aggregate_strategy_metrics(evaluated_trades)
        
        assert len(strategy_metrics) > 0, "Integration should produce results"

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
